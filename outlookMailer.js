
const axios = require("axios"); // Standardize on axios

// --- Configuration ---
const OUTLOOK_CLIENT_ID = "4159d2c0-14f4-4513-8c77-1a6965e74ed5";
const OUTLOOK_CLIENT_SECRET = "****************************************";
const OUTLOOK_REDIRECT_URI = "https://backend.automatebusiness.com/api/oauth2callback";
const OUTLOOK_TENANT_ID = "common";

const OUTLOOK_AUTHORIZE_ENDPOINT = `https://login.microsoftonline.com/${OUTLOOK_TENANT_ID}/oauth2/v2.0/authorize`;
const OUTLOOK_TOKEN_ENDPOINT = `https://login.microsoftonline.com/${OUTLOOK_TENANT_ID}/oauth2/v2.0/token`;
const OUTLOOK_GRAPH_API_BASE = "https://graph.microsoft.com/v1.0";

/**
 * Generates the Outlook authorization URL.
 * @returns {string} The authorization URL.
 */
function getOutlookAuthUrl() {
  const scopes = [
    "openid",
    "email",
    "profile",
    "offline_access",
    "Mail.Send",
    "User.Read",
    "Calendars.ReadWrite", // Added for calendar access
  ];

  const params = new URLSearchParams({
    client_id: OUTLOOK_CLIENT_ID,
    response_type: "code",
    redirect_uri: OUTLOOK_REDIRECT_URI,
    scope: scopes.join(" "),
    response_mode: "query",
    prompt: "consent", // Forces consent screen for development
  });

  return `${OUTLOOK_AUTHORIZE_ENDPOINT}?${params.toString()}`;
}

/**
 * Exchanges the authorization code for access and refresh tokens. This is a stateless function.
 * @param {string} code - The authorization code from the callback.
 * @returns {Promise<Object>} An object containing access_token, refresh_token, etc.
 */

async function exchangeOutlookCodeForTokens(code) {
  const params = new URLSearchParams({
    client_id: OUTLOOK_CLIENT_ID,
    client_secret: OUTLOOK_CLIENT_SECRET,
    code: code,
    redirect_uri: OUTLOOK_REDIRECT_URI,
    grant_type: "authorization_code",
  });

  try {
    const response = await axios.post(OUTLOOK_TOKEN_ENDPOINT, params, {
      headers: { "Content-Type": "application/x-www-form-urlencoded" },
    });
    // This function is now stateless. It returns the tokens for the caller to store.
    return response.data;
  } catch (error) {
    console.error(
      "Error exchanging Outlook code:",
      error.response?.data || error.message
    );
    throw new Error("Failed to exchange Outlook code for tokens.");
  }
}


/**
 * Uses a refresh token to get a new access token.
 * @param {string} refreshToken - The stored refresh token.
 * @returns {Promise<string>} A new access token.
 */
async function refreshOutlookAccessToken(refreshToken) {
  console.log("Refreshing Outlook access token...");
  const params = new URLSearchParams({
    client_id: OUTLOOK_CLIENT_ID, // BUG FIX: Was CLIENT_ID
    client_secret: OUTLOOK_CLIENT_SECRET, // BUG FIX: Was CLIENT_SECRET
    grant_type: "refresh_token",
    refresh_token: refreshToken,
  });

  try {
    const response = await axios.post(OUTLOOK_TOKEN_ENDPOINT, params, {
      headers: { "Content-Type": "application/x-www-form-urlencoded" },
    });
    console.log("Successfully obtained new access token.");
    return response.data.access_token;
  } catch (error) {
    console.error(
      "Error refreshing access token:",
      error.response?.data || error.message
    );
    throw new Error("Could not refresh access token.");
  }
}

/**
 * Gets the user's profile from the Microsoft Graph API.
 * @param {string} accessToken - The current access token.
 * @returns {Promise<Object>} The user's profile object.
 */

async function getOutlookUserProfile(accessToken) {
  try {
    const response = await axios.get(`${OUTLOOK_GRAPH_API_BASE}/me`, {
      headers: { Authorization: `Bearer ${accessToken}` },
    });
    return response.data;
  } catch (error) {
    console.error(
      "Error fetching Outlook user profile:",
      error.response?.data || error.message
    );
    throw new Error("Failed to fetch Outlook user profile.");
  }
}

/**
 * Sends an email via Microsoft Graph API using a refresh token.
 * @param {Object} options - Email sending options.
 * @param {string} options.refreshToken - The user's stored refresh token.
 * @param {string} options.to - Comma-separated recipient email addresses.
 * @param {string} options.subject - Email subject.
 * @param {string} options.htmlBody - Email body in HTML.
 * @param {string} [options.cc] - Comma-separated CC recipient email addresses.
 * @param {string} [options.bcc] - Comma-separated BCC recipient email addresses.
 * @param {Array} [options.attachments] - Array of attachment objects { filename, contentType, contentBytes (Base64 string) }.
 * @returns {Promise<Object>} - Result of the send operation.
 */


async function sendOutlookEmail(options) {
  const { REFRESH_TOKEN, to, subject, htmlBody, cc, bcc, attachments = [] } =
    options;

  try {
    const accessToken = await refreshOutlookAccessToken(REFRESH_TOKEN);

    const attachmentsPayload = attachments.map((att) => ({
      "@odata.type": "#microsoft.graph.fileAttachment",
      name: att.filename,
      contentType: att.contentType,
      contentBytes: att.content.toString("base64"),
    }));

    const emailPayload = {
      message: {
        subject: subject,
        body: { contentType: "HTML", content: htmlBody },
        toRecipients: Array.isArray(to)
          ? to.map((e) => ({ emailAddress: { address: e.trim() } }))
          : [{ emailAddress: { address: to.trim() } }],
        ccRecipients: Array.isArray(cc)
          ? cc.map((e) => ({ emailAddress: { address: e.trim() } }))
          : [],
        bccRecipients: Array.isArray(bcc)
          ? bcc.map((e) => ({ emailAddress: { address: e.trim() } }))
          : [],
        attachments: attachmentsPayload,
      },
      saveToSentItems: "true",
    };

    await axios.post(`${OUTLOOK_GRAPH_API_BASE}/me/sendMail`, emailPayload, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
    });

    console.log("Email sent successfully via Graph API.");
    return { success: true, message: "Email sent successfully." };
  } catch (error) {
    console.error(
      "Error in sendOutlookEmail service:",
      error.response?.data?.error || error.message
    );
    throw new Error("Failed to send email via Outlook service.");
  }
}


module.exports = {
  getOutlookAuthUrl,
  exchangeOutlookCodeForTokens,
  getOutlookUserProfile,
  sendOutlookEmail
};