const express = require("express");
const multer = require("multer");
const { google } = require("googleapis");


// const upload = multer({
//   limits: {
//     fileSize: 5 * 1024 * 1024, // 5 MB limit per file
//   },
//   dest: "uploads/"
// });


const uploadToMemory = multer({
  // storage: multer.memoryStorage(), // For CSV processing
   limits: {
     fileSize: 5 * 1024 * 1024, // 5 MB limit per file
   }
 });
 
 const uploadToDisk = multer({
   limits: {
     fileSize: 5 * 1024 * 1024, // 5 MB limit per file
   },
   dest: "uploads/" // For email attachments
 });
 
 
const router = express.Router();
const fetch = require("node-fetch");
const fs = require("fs");
const path = require("path");
const csv = require('csv-parser');
const axios = require("axios");


const {
  uploadLabeledImages,
  getDescriptorsFromDB,
  supabase,
  replaceTags,
  fetchAndMergeData,
  compareImageWithAllOrientations,
  getValuefromdatabase,
  getEmailRecord,
  replaceTemplateVariables,
  logImportHistory,
  getAttachments,
  sendDynamicMessage,
  compareImageWithWorkspace,
  log_product_import,
  getAttachmentFromUrl
  
} = require("./helpers");
const {sendEmail, oAuth2Client, sendZeptomail,sendAdminEmail } = require("./mailer");

const {getOutlookAuthUrl,exchangeOutlookCodeForTokens,getOutlookUserProfile,sendOutlookEmail } = require("./outlookMailer");

const { send } = require("process");

router.get("/auth-url", (req, res) => {
  const scopes = [
    "https://www.googleapis.com/auth/userinfo.email",
    "https://www.googleapis.com/auth/userinfo.profile",
    "https://www.googleapis.com/auth/gmail.send",
    // "https://www.googleapis.com/auth/gmail.addons.current.action.compose",
    // "https://www.googleapis.com/auth/gmail.addons.current.message.action"
    // "https://mail.google.com/",
  ];

  // Get the origin from the query parameters
  // const { origin } = req.query;

  const url = oAuth2Client.generateAuthUrl({
    access_type: "offline",
    scope: scopes,
    prompt: "consent",
    // Include the origin in the state parameter
    // state: origin ? JSON.stringify({ origin }) : undefined,
  });
  console.log(url);
  res.json({ url });
});

/**
 * @route   GET /outlook/auth-url
 * @desc    Generates and returns the Microsoft authorization URL for user consent.
 * @access  Public
 */
router.get("/outlook-auth-url", (req, res) => {
  try {
    // 1. Call the service function that constructs the URL.
    //    All the logic (scopes, client_id, etc.) is neatly contained there.
    const authUrl = getOutlookAuthUrl();

    // 2. Log the URL for debugging purposes on the server.
    console.log("Generated Outlook Auth URL:", authUrl);

    // 3. Send the URL back to the client in a JSON object,
    //    exactly like your Gmail API example.
    res.json({ url: authUrl });
  } catch (error) {
    console.error("Error generating Outlook auth URL:", error);
    res
      .status(500)
      .json({ error: "Failed to generate Outlook authorization URL." });
  }
});

router.get("/auth-url-calendar", (req, res) => {
  const scopes = [
    "https://www.googleapis.com/auth/calendar.events",
     "https://www.googleapis.com/auth/calendar.readonly",
     
    "https://www.googleapis.com/auth/userinfo.email",
    "https://www.googleapis.com/auth/userinfo.profile",
  ];

  // Get the origin from the query parameters
  // const { origin } = req.query;

  const url = oAuth2Client.generateAuthUrl({
    access_type: "offline",
    scope: scopes,
    prompt: "consent",
    // Include the origin in the state parameter
    // state: origin ? JSON.stringify({ origin }) : undefined,
  });
  console.log(url);
  res.json({ url });
});

router.get("/oauth2callback", (req, res) => {
  const code = req.query.code;

  if (!code) {
    return res.status(400).send("Authorization code not found");
  }

  // Redirect to the desired URL with the code parameter
  res.redirect(`https://crm.automatebusiness.com/auth.html?code=${code}`);
});

router.post("/exchange-code", async (req, res) => {
  const { code, user_id } = req.body;

  try {
    let tokens;
    if (code.startsWith("4/")) {
      // Web auth code
      tokens = await oAuth2Client.getToken(code);
    } else {
      // Native auth code
      tokens = await oAuth2Client.getToken({
        code: code,
        grant_type: "authorization_code",
      });
    }
    // Set the credentials on the OAuth2 client
    oAuth2Client.setCredentials(tokens.tokens);
    const people = google.people({ version: "v1", auth: oAuth2Client });

    // Fetch the user's profile information
    const profile = await people.people.get({
      resourceName: "people/me",
      personFields: "emailAddresses",
    });

    const email = profile.data.emailAddresses[0].value;

    const { refresh_token, access_token } = tokens.tokens;
    console.log(refresh_token);
    if (refresh_token) {
      // Store the refresh token securely (use a database in production)
      await supabase
        .from("workspace_email_credentials")
        .delete()
        .eq("user_id", user_id);
      const { error } = await supabase
        .from("workspace_email_credentials")
        .insert({
          refresh_token,
          sender_mail_id: email,
          user_id: user_id,
          email_client : "gmail"
        });
      if (error) console.log(error);
      // refreshTokens.set(access_token, refresh_token);
    }
    res.json({ access_token });
  } catch (error) {
    console.error("Error exchanging code:", error);
    res.status(400).json({ error: "Failed to exchange code" });
  }
});

/**
 * @route   POST /outlook/exchange-code
 * @desc    Exchanges an Outlook authorization code for tokens, fetches the user's email,
 *          and stores the refresh token in the database.
 * @access  Private (should be called by an authenticated user)
 */
router.post("/outlook-exchange-code", async (req, res) => {
  // 1. Get the code and user_id from the request body
  const { code, user_id } = req.body;

  if (!code || !user_id) {
    return res
      .status(400)
      .json({ error: "Request body must contain 'code' and 'user_id'." });
  }

  try {
    // 2. Exchange the one-time code for tokens using your service function
    const tokens = await exchangeOutlookCodeForTokens(code);
    const { access_token, refresh_token } = tokens;

    if (!access_token) {
      throw new Error("Did not receive an access token from Outlook.");
    }

    // 3. Use the new access token to get the user's profile (and email)
    const profile = await getOutlookUserProfile(access_token);
    const email = profile.mail || profile.userPrincipalName;

    if (!email) {
      throw new Error("Could not retrieve user's email from Outlook profile.");
    }

    // 4. If a refresh token is returned, securely store it in the database
    //    (A refresh token is typically only returned the first time a user consents)
    if (refresh_token) {
      console.log(
        "Received new Outlook refresh token. Storing securely in database..."
      );

      // First, delete any old Outlook credentials for this user to prevent duplicates
      const { error: deleteError } = await supabase
        .from("workspace_email_credentials")
        .delete()
        .eq("user_id", user_id)
        .eq("email_client", "outlook"); // Use the provider column to specify Outlook

      if (deleteError) {
        console.error("Error deleting old Outlook credentials:", deleteError);
        // Decide if this is a critical error. For now, we'll proceed.
      }

      // Now, insert the new credentials
      const { error: insertError } = await supabase
        .from("workspace_email_credentials")
        .insert({
          refresh_token: refresh_token, // IMPORTANT: Encrypt this in a real production app
          sender_mail_id: email,
          user_id: user_id,
          email_client : "outlook"
        });

      if (insertError) {
        // If the insert fails, we have a serious problem.
        console.error("Failed to insert Outlook credentials:", insertError);
        throw new Error("Could not save credentials to the database.");
      }
    } else {
      console.log(
        "No new refresh token received. User has likely already granted consent."
      );
    }

    // 5. Finally, send the short-lived access token back to the client.
    //    The client can use this for immediate API calls if needed.
    res.json({ access_token });
  } catch (error) {
    console.error("Error during Outlook code exchange:", error.message);
    res
      .status(500)
      .json({ error: "Failed to exchange Outlook code and store credentials." });
  }
});

// exchange code for calendar
router.post("/exchange-code-calendar", async (req, res) => {
  const { code, user_id,workspace_id} = req.body;

  try {
    let tokens;
    if (code.startsWith("4/")) {
      // Web auth code
      tokens = await oAuth2Client.getToken(code);
    } else {
      // Native auth code
      tokens = await oAuth2Client.getToken({
        code: code,
        grant_type: "authorization_code",
      });
    }
    // Set the credentials on the OAuth2 client
    oAuth2Client.setCredentials(tokens.tokens);
    
    const { refresh_token, access_token } = tokens.tokens;
    console.log(refresh_token);

    const people = google.people({ version: "v1", auth: oAuth2Client });

    // Fetch the user's profile information
    const profile = await people.people.get({
      resourceName: "people/me",
      personFields: "emailAddresses",
    });

    const email = profile.data.emailAddresses[0].value;

    if (refresh_token) {
      
        
      // Store the refresh token securely (use a database in production)
      await supabase
        .from("crm_calendar_credentials")
        .delete()
        .eq("user_id", user_id);
      const { error } = await supabase
        .from("crm_calendar_credentials")
        .insert({
          refresh_token,
          workspace_id: workspace_id,
          user_id: user_id,
          calendar_id : email,
           
        });
      if (error) console.log(error);
      
      // refreshTokens.set(access_token, refresh_token);
    }
    res.json({ access_token });
  } catch (error) {
    console.error("Error exchanging code:", error);
    res.status(400).json({ error: "Failed to exchange code" });
  }
});

router.post("/exchange-code-ios", async (req, res) => {
  const { code, user_id } = req.body;
  const oAuth2Clientios = new google.auth.OAuth2(
    "778802178451-9gtgcd6eo5iutgue4ibubb6h4orqo951.apps.googleusercontent.com",
    CLIENT_SECRET,
    REDIRECT_URI
  );
  try {
    let tokens;
    if (code.startsWith("4/")) {
      // Web auth code
      tokens = await oAuth2Clientios.getToken(code);
    } else {
      // Native auth code
      tokens = await oAuth2Clientios.getToken({
        code: code,
        grant_type: "authorization_code",
      });
    }
    // Set the credentials on the OAuth2 client
    oAuth2Clientios.setCredentials(tokens.tokens);
    const people = google.people({ version: "v1", auth: oAuth2Client });

    // Fetch the user's profile information
    const profile = await people.people.get({
      resourceName: "people/me",
      personFields: "emailAddresses",
    });

    const email = profile.data.emailAddresses[0].value;

    const { refresh_token, access_token } = tokens.tokens;
    console.log(refresh_token);
    if (refresh_token) {
      // Store the refresh token securely (use a database in production)
      const { error } = await supabase
        .from("workspace_email_credentials")
        .insert({
          refresh_token,
          sender_mail_id: email,
          user_id: user_id,
        });
      if (error) console.log(error);
      // refreshTokens.set(access_token, refresh_token);
    }
    res.json({ access_token });
  } catch (error) {
    console.error("Error exchanging code:", error);
    res.status(400).json({ error: "Failed to exchange code" });
  }
});

router.post("/send-email", (req, res, next) => {
  uploadToDisk.array("attachments")(req, res, function (err) {
    if (err instanceof multer.MulterError && err.code === "LIMIT_FILE_SIZE") {
      return res.status(400).json({ error: "Each attachment must be less than 5 MB" });
    } else if (err) {
      console.log("File upload error",err.message)
      //return res.status(400).json({ error: "File upload error", details: err.message });
    }
    next();
  });
}, async (req, res) => {

  try {
    console.log("Received request to /send-email");

    const { user_id, to, subject, html, cc, bcc, leadId } = req.body;
    const attachments = req.files;
    // console.log("Received files:", req.files);
    // console.log("Received body:", req.body);
    if (!user_id || !to || !subject || !html || !leadId) {
      throw new Error(
        "user_id, to, subject, leadId and html are mandatory. Please provide all the mandatory fields"
      );
    }

    const { data: credentials, error: apiKeyError } = await supabase
      .from("workspace_email_credentials")
      .select("*")
      .eq("user_id", user_id)
      .single();

    if (apiKeyError || !credentials) {
      console.log("Error in Fetching credentials");
      console.log(apiKeyError);
      return res.status(401).json({ error: "Invalid or inactive API key" });
    }

    const { data: userProfile, error: userProfileError } = await supabase
      .from("user_profile")
      .select("*")
      .eq("id", user_id)
      .single();

    if (userProfileError || !userProfile) {
      console.log(userProfileError);
      return res
        .status(400)
        .json({ error: "User not found or error in fetching user profile" });
    }

    const workspaceId = userProfile.workspace_id;

    let tags = await fetchAndMergeData(leadId, workspaceId);
    const processedHtml = replaceTags(html, tags);
    const processedSubject = replaceTags(subject, tags);

    // console.log("processed html is : ", processedHtml);

    const REFRESH_TOKEN = credentials.refresh_token;
    const senderMail = credentials.sender_mail_id;
    const senderName = `${userProfile.first_name} ${userProfile.last_name}`;


    const mail_client = credentials.email_client;

   // console.log('mail_client : ',mail_client)

    let emailInfo;
    try {

    if(mail_client=='gmail'){
      emailInfo = await sendEmail({
        REFRESH_TOKEN,
        senderMail,
        senderName,
        to,
        processedSubject,
        processedHtml,
        cc,
        bcc,
        attachments,
      });
    }else
    if(mail_client=='outlook'){
   
      emailInfo = await sendOutlookEmail({
        REFRESH_TOKEN,
       // senderMail,
       // senderName,
        to,
        subject : processedSubject,
        htmlBody : processedHtml,
        cc,
        bcc,
        attachments,
      });
    }
    


    } catch (error) {
      console.error("Error sending email:", error);
      return res.status(500).json({ 
        error: "Failed to send email",
        details: error.message 
      });
    }


    

    await supabase.from("email_log").insert({
      event: 'Send Email to Lead',
      sent_to: to,
      workspace_id: workspaceId,
      sending_email: senderMail,
      cc: cc,
      bcc: bcc,
      subject: processedSubject,
      lead_id: leadId,
      user_id: user_id,
    });
    res.send(emailInfo);
  } catch (error) {
    console.error("Error in /send-email route:", error);
    res.status(500).json({ error: error.message, stack: error.stack });
  }
});


router.post("/post-face", async (req, res) => {
  const File1 = req.files.File1.tempFilePath;
  const File2 = req.files.File2.tempFilePath;
  const File3 = req.files.File3.tempFilePath;
  const label = req.body.label;
  let result = await uploadLabeledImages([File1, File2, File3], label);
  if (result) {
    res.json({ message: "Face data stored successfully" });
  } else {
    res.json({ message: "Something went wrong, please try again." });
  }
});

router.post("/register-face", async (req, res) => {
  const { id } = req.body;
  const { data, error } = await supabase
    .from("faces_db")
    .select()
    .eq("id", id)
    .single();
  if (error) console.log(error);
  await uploadLabeledImages(data);
  res.json({ message: "Face data stored successfully" });
});

router.post("/check-face", async (req, res) => {
  //   const File1 = req.files.File1.tempFilePath;
  const File1 = req.body.file;
  const user_id = req.body.user_id;
  console.log(File1);
  // let result = await getDescriptorsFromDB(
  //   `https://dudkeydyykleosvsoxgk.supabase.co/storage/v1/object/public/${File1}`,
  //   user_id
  // );
  let result = await compareImageWithAllOrientations(
    `https://dudkeydyykleosvsoxgk.supabase.co/storage/v1/object/public/${File1}`,
    user_id
  );
  console.log(result);
  res.json({ result });
});

router.post("/check-face-workspace", async (req, res) => {
  //   const File1 = req.files.File1.tempFilePath;
  const File1 = req.body.file;
  const wid = req.body.wid;
  console.log(File1);
  // let result = await getDescriptorsFromDB(
  //   `https://dudkeydyykleosvsoxgk.supabase.co/storage/v1/object/public/${File1}`,
  //   user_id
  // );
  let result = await compareImageWithWorkspace(
    `https://dudkeydyykleosvsoxgk.supabase.co/storage/v1/object/public/${File1}`,
    wid
  );
  console.log(result);
  res.json({ result });
});

router.post("/send-reminder", async (req, res) => {
  try {
    const event_id = req.body.event_id;
    const time = req.body.time;
    const unit = req.body.unit;
    console.log(event_id, time, unit);
    if (!event_id) {
      return res.status(400).send("event_id parameter is required");
    }
    // Fetch workspace record
    const { data: eventRecord, error: ws_Error } = await supabase
      .from("events")
      .select("*")
      .eq("id", event_id)
      .single();
    console.log("eventRecord", eventRecord);
    if (ws_Error) {
      throw ws_Error;
    }
    const event_members = eventRecord.event_members;
    console.log("event_members : ", event_members);
    for (let i = 0; i < event_members.length; i++) {
      const user_profile_record = await getValuefromdatabase(
        "user_profile",
        "id",
        event_members[i]
      );
      const emailRecord = await getEmailRecord("event_reminder");
      const variables = {
        Name: `${user_profile_record.first_name} ${user_profile_record.last_name}`,
        Event_name: eventRecord.event_name,
        time: time,
        unit: unit,
        link: eventRecord.link,
      };
      const subjectVars = {
        Event_name: eventRecord.event_name,
        time: time,
        unit: unit,
      };
      const emailBody = replaceTemplateVariables(
        emailRecord.html_content,
        variables
      );
      const emailSubject = replaceTemplateVariables(
        emailRecord.subject,
        subjectVars
      );
      if (emailRecord) {
        await sendZeptomail(
          user_profile_record.email,
          emailSubject,
          "",
          emailBody
        );
      }
    }
    res.status(200).send("Reminders sent successfully");
  } catch (error) {
    console.error("Error sending reminders:", error);
    res.status(500).send("Internal Server Error");
  }
});

// send bulk email in CRM by gmail auth

router.post("/send-bulk-email",uploadToDisk.array("attachments"),
  async (req, res) => {
    try {
      console.log("Received request to /send-bulk-email");

      const { user_id, subject, html, cc, bcc, leadIds, event_name } = req.body;
      console.log(user_id, subject, html, cc, bcc, leadIds, event_name);

      const attachments = req.files;

      if (!user_id || !subject || !html || !leadIds) {
        throw new Error(
          "user_id, subject, leadId and html body are mandatory. Please provide all the mandatory fields"
        );
      }

      const { data: credentials, error: apiKeyError } = await supabase
        .from("workspace_email_credentials")
        .select("*")
        .eq("user_id", user_id)
        .single();

      if (apiKeyError || !credentials) {
        console.log("Error in Fetching credentials");
        console.log(apiKeyError);
        return res.status(401).json({ error: "Invalid or inactive API key" });
      }

      const { data: userProfile, error: userProfileError } = await supabase
        .from("user_profile")
        .select("*")
        .eq("id", user_id)
        .single();

      if (userProfileError || !userProfile) {
        console.log(userProfileError);
        return res
          .status(400)
          .json({ error: "User not found or error in fetching user profile" });
      }

      const workspaceId = userProfile.workspace_id;

      const REFRESH_TOKEN = credentials.refresh_token;
      const senderMail = credentials.sender_mail_id;
      const senderName = credentials.from_name;
      const mail_client = credentials.email_client;

      for (let i = 0; i < leadIds.length; i++) {
        let leadId = leadIds[i];
        const { data: lead_record, error: leadError } = await supabase
          .from("crm_leads_view")
          .select("*")
          .eq("id", leadId)
          .single();

        if (leadError || !lead_record) {
          console.error(`Error fetching lead data for ID: ${leadId}`, leadError);
          continue; // Skip this lead and continue processing others
        }

        let to = lead_record.contact_email;
        if(to && to!=='') {
        let tags = await fetchAndMergeData(leadId, workspaceId);

        const processedHtml = replaceTags(html, tags);
        const processedSubject = replaceTags(subject, tags);

        const info = await sendEmail({
          REFRESH_TOKEN,
          senderMail,
          senderName,
          to,
          processedSubject,
          processedHtml,
          cc,
          bcc,
          attachments,
        });

        

      if(mail_client=='gmail'){
        emailInfo = await sendEmail({
          REFRESH_TOKEN,
          senderMail,
          senderName,
          to,
          processedSubject,  
        processedHtml, 
          cc,
          bcc,
          attachments,
        });
      }else
      if(mail_client=='outlook'){
     
        emailInfo = await sendOutlookEmail({
          REFRESH_TOKEN,
         // senderMail,
         // senderName,
          to,
          subject : processedSubject,
          htmlBody : processedHtml,
          cc,
          bcc,
          attachments,
        });
      }
        console.log("Email info:", info);

        if (info && info.messageId) {
          const { error } = await supabase.from("email_log").insert({
            event: event_name,
            sent_to: to,
            workspace_id: workspaceId,
            sending_email: senderMail,
            cc: cc,
            bcc: bcc,
            subject: processedSubject,
            lead_id: leadId,
            user_id: user_id,
          });

          if (error) {
            console.error("Error inserting into email_log:", error);
          }
        } else {
          console.error("Failed to send email to:", to);
        }

      }
      }

      res.status(200).json({ message: "Emails sent successfully" });
    } catch (error) {
      console.error("Error in /send-bulk-email route:", error);
      res.status(500).json({ error: error.message });
    }
  }
);

// send bulk email in admin
router.post("/sendbulkemails", uploadToDisk.array("files"), async (req, res) => {
  try {
    const { workspace_ids, subject, email_body, cc, attachments,user_id } = req.body;

    if (workspace_ids && workspace_ids.length > 0) {
      for (const workspace_id of workspace_ids) {
        const ownerWorkspaceRecord = await getValuefromdatabase(
          "workspace_details",
          "id",
          workspace_id
        );

        

        if (ownerWorkspaceRecord) {
        
          let personalizedSubject = subject;
          let personalizedBody = email_body;

          // Replace placeholders with workspace-specific values
          Object.keys(ownerWorkspaceRecord).forEach((variable) => {
            const replacementValue = ownerWorkspaceRecord[variable] || "";
            
            personalizedSubject = personalizedSubject.replaceAll(
              `{{${variable}}}`,
              replacementValue
            );
            personalizedBody = personalizedBody.replaceAll(
              `{{${variable}}}`,
              replacementValue
            );
          });

          // Process attachments
          const attachmentUrls = Array.isArray(attachments) ? attachments : [];
          const allAttachments = await getAttachments(attachmentUrls, req.files);

          // Send email
          await sendAdminEmail(
            ownerWorkspaceRecord.owner_email,
            personalizedSubject,
            "",
            personalizedBody,
            cc,
            allAttachments,
            workspace_id,
            user_id
          );
        }
      }
    }

    res.status(200).send("Emails sent successfully");
  } catch (error) {
    console.error("Error sending emails:", error);
    res.status(500).send("Internal Server Error.");
  }
});




// bulk upload lead


router.post("/uploadBulkLeads", uploadToMemory.single('csvFile'), async (req, res) => {
  // console.log(req);
   const workspaceId =  req.body.workspace_id;//Number(req.query.workspace_id);
   const imported_by = req.body.imported_by;//req.query.imported_by;
   const reassign = req.body.reassign;
 
 
   console.log('workspaceId,imported_by,reassign : ',workspaceId,imported_by,reassign);
   if (!workspaceId || !imported_by || !reassign) {
     return res.status(400).json({ error: 'Missing workspace_id or imported_by or reassign settings' });
   }
 
   if (!req.file) {
     return res.status(400).json({ error: 'Missing CSV file' });
   }
 
   //const filePath = path.join(__dirname, req.file.path); // Path to the uploaded file
   const fileName = req.file.originalname; // Get the original file name
 
   console.log('fileName:', fileName); 
 
   const fileExtension = fileName.split('.').pop().toLowerCase();
   
   if (fileExtension !== 'csv') {
     return res.status(400).json({ error: 'Invalid file format. Only CSV files are allowed.' });
   }
   
   // Additionally, check MIME type if available
   if (req.file.mimetype && req.file.mimetype !== 'text/csv') {
     return res.status(400).json({ error: 'Invalid file type. Only CSV files are allowed.' });
   }
   // Parse CSV content
   const dataRows = [];
   const requiredColumns = ['first_name', 'email', 'phone', 'pipeLine_name', 'stage','assigned_to_email'];
   
   let headers = [];
     
 
   let contactFailedLogs = [], leadFailedLogs = [], companyFailedLogs = [];
   let contact_skipped_Logs = [], lead_skipped_Logs = [], company_skipped_Logs = [];
   
   let leadSuccessCount = 0,lead_skip_count = 0, leadFailureCount = 0;
   let contactSuccessCount = 0, contactFailureCount = 0, contactSkipCount = 0;
   let companySuccessCount = 0, companyFailureCount = 0, companySkipCount = 0;
   let formattedData = [];
   let lead_ids_array = [];
   let contact_ids_array = [];
   let company_ids_array = [];
         
  
 
   // Parse CSV content from in-memory buffer
 const bufferStream = require('stream').Readable.from(req.file.buffer); // Convert buffer to readable stream
 
 bufferStream
   .pipe(csv())
   .on('headers', (headerList) => {
     headers = headerList;
   })
   .on('data', (row) => {
     formattedData = headers.map(header => row[header]);
     dataRows.push(row);
   })
   
     .on('end', async () => {
        // console.log('Data Rows:', dataRows.length);
       const missingColumns = requiredColumns.filter(col => !headers.includes(col));
       if (missingColumns.length > 0) {
         
         leadFailedLogs.push({ row:formattedData, error: `Missing required columns: ${missingColumns.join(', ')}` });
     const  formatted_lead_failed_logs = {
         head: headers,
         data: leadFailedLogs 
       };
       await logImportHistory({ source: fileName, module: 'Leads', added: 0, skipped: 0, imported_by:imported_by,workspace_id : workspaceId,failed_logs :formatted_lead_failed_logs,failed : dataRows.length});
    
         return res.status(400).json({ error: `Missing required columns: ${missingColumns.join(', ')}` });
       }
 
       
       // Fetch custom fields
       const { data: allCustomFields, error: fetchCustomFieldError } = await supabase
         .from('crm_leads_custom_fields_metadata')
         .select('*')
         .eq('workspace_id', workspaceId);
       
       if (fetchCustomFieldError) {
         return res.status(500).json({ error: 'Error fetching custom fields' });
       }
       
       
 
       for (const row of dataRows) {
         try{
        // console.log('row : ' ,row)
         let stageId = '', assignedToId = '', companyId = '', contactId = '';
         const pipeline_name = row.pipeLine_name; //row.pipeLine_name;
         const stage_name =  row.stage; 
         // Fetch pipeline
         const { data: existingPipeline, error: fetchPipelineError } = await supabase
           .from('crm_pipeline')
           .select('id')
           .eq('name', pipeline_name)
           .eq('workspace_id', workspaceId)
           .single();
 
         if (!existingPipeline || fetchPipelineError) {
          
           leadFailedLogs.push({ row: headers.map(header => row[header]), error: `Pipeline ${pipeline_name} not exists.` });
           leadFailureCount++;
           continue;
         }
         const pipeline_id = existingPipeline.id;
         //console.log('pipeline_id',pipeline_id)
         // Fetch stage
         const { data: existingStage, error: fetchStageError } = await supabase
           .from('crm_stages')
           .select('*')
           .eq('name', stage_name)
           .eq('pipeline', pipeline_id)
           .single();
 
         if (!existingStage || fetchStageError) {
           leadFailedLogs.push({ row: headers.map(header => row[header]), error: `Stage ${stage_name} not exists.` });
           leadFailureCount++;
           continue;
         } else {
           stageId = existingStage.id;
         }
         //console.log('pipeline_id',pipeline_id)
         // Fetch assigned user profile
         const { data: userProfile, error: userProfileError } = await supabase
           .from('user_profile')
           .select('id')
           .eq('email', row.assigned_to_email)
           .eq('workspace_id', workspaceId)
           .single();
 
         if (userProfileError || !userProfile) {
            // console.log('userProfileError',userProfileError);
           leadFailedLogs.push({ row: headers.map(header => row[header]), error: `Salesperson ${row.assigned_to_email} not exists.` });
           leadFailureCount++;
           continue;
         }
         const user_id = userProfile.id;
         //console.log('user_id',user_id)
         const { data: crmTeam, error: crmTeamError } = await supabase
           .from('crm_team')
           .select('id')
           .eq('user_id', user_id)
           .single();
 
         if (crmTeamError || !crmTeam) {
           leadFailedLogs.push({ row: headers.map(header => row[header]), error: `${row.assigned_to_email} could not be found in CRM Team.` });
           leadFailureCount++;
           continue;
         }
         assignedToId = crmTeam.id;
 
         //console.log('assignedToId : ',assignedToId);
 
         // Check if company exists or insert new company
         const company_name = row.company_name;
         if (company_name) {
           const { data: existingCompany, error: fetchCompanyError } = await supabase
             .from('crm_companies')
             .select('*')
             .eq('name', company_name)
             .eq('workspace_id', workspaceId)
             .single();
 
           if (existingCompany) {
             companySkipCount++;
             companyId = existingCompany.id;
             company_skipped_Logs.push({ row: headers.map(header => row[header]), error: `Company Already Exist with this name.` });
           
             
           } else {
             const companyData = {
               name: company_name,
               workspace_id: workspaceId,
               website: row.website,
               tax_no: row.tax_no,
               address: row[columnIndices.company_billing_address],
                    shipping_address: row[columnIndices.company_shipping_address],
                    city : row[columnIndices.company_city],
                    state : row[columnIndices.company_state],
                    pincode : row[columnIndices.company_pincode],
                    country : row[columnIndices.company_country],
             };
             const { data: newCompany, error: companyError } = await supabase
               .from('crm_companies')
               .insert(companyData)
               .select('id')
               .single();
 
             if (companyError) {
               companyFailureCount++;
               companyFailedLogs.push({ row: headers.map(header => row[header]), error: companyError.message });
               continue;
             }
             companyId = newCompany.id;
             console.log('companyId : ',companyId);
 
             companySuccessCount++;
             company_ids_array.push(newCompany.id);
           }
         } else {
           companyId = null;
         }
         console.log('companyId : 2 ',companyId);
         // Sanitize the phone number by removing spaces and non-numeric characters
         const sanitizedPhone = row.phone.replace(/\D/g, '');
         const sanitizedEmail = String(row.email).trim();
       
 
         // Check if contact exists or insert new contact
         const { data: existingContact, error: fetchContactError } = await supabase
         .from('crm_contacts')
         .select('*')
         .eq('phone', sanitizedPhone)
         .eq('email',sanitizedEmail )
         .eq('workspace_id', workspaceId)
         .single();

 console.log('existingContact : ',existingContact);

         if (existingContact) {
           contactSkipCount++;
           contactId = existingContact.id;
           contact_skipped_Logs.push({ row: headers.map(header => row[header]), error: 'Contact Already exist.' });
           
         } else {
           const contactData = {
             first_name: row.first_name,
             last_name: row.last_name,
             email: row.email,
             phone: sanitizedPhone || null,
             address: row[columnIndices.address] || null,
             country: row[columnIndices.country] || null,
             country_code: row[columnIndices.country_code] || '91',
             city: row[columnIndices.city] || null,
             state: row[columnIndices.state] || null,
             pincode: row[columnIndices.pincode] || null,
             designation: row[columnIndices.designation] || null,
             dob: row[columnIndices.date_of_birth] || null,
             doa: row[columnIndices.date_of_anniversary] || null,
             workspace_id: workspaceId,
             company_id: companyId,
             is_active: true,
             image_url: ''
           };
 
           const { data: newContact, error: contactError } = await supabase
             .from('crm_contacts')
             .insert(contactData)
             .select('id')
             .single();
 
           if (contactError) {
             contactFailureCount++;
             contactFailedLogs.push({ row: headers.map(header => row[header]), error: contactError.message });
             continue;
           }
           contactId = newContact.id;
           contactSuccessCount++;
           contact_ids_array.push(newContact.id);
         }
         console.log('contactId : ',contactId);
 
            // Function to find matching field IDs
 const findMatchingFieldIds = (headers, allCustomFields) => {
   return headers.reduce((acc, header) => {
     const match = allCustomFields.find(field => field.name.toLowerCase() === header.toLowerCase() && field.pipeline_id === pipeline_id);
     if (match) acc.push({ header, id: match.id, type: match.type, dropdown: match.dropdown });
     return acc;
   }, []);
 };
 
 const availableCustomFields = findMatchingFieldIds(headers, allCustomFields);
 
// console.log('availableCustomFields :', availableCustomFields);
 let customFieldsFlag = false;
 
 if (availableCustomFields.length > 0) {
   for (const field of availableCustomFields) {
     const fieldValue = row[field.header] || null;
 
     // Skip validation for blank values
     if (fieldValue === null || fieldValue === undefined || fieldValue === '') {
       continue;
     }
 
     // Validate value based on the type
     if (field.type === 'Date') {
       const dateRegex = /^\d{4}-\d{2}-\d{2}$/; // Regex for YYYY-MM-DD format
       if (!dateRegex.test(fieldValue) || isNaN(Date.parse(fieldValue))) {
         console.error(`Invalid date value for field: ${field.header}`);
         leadFailedLogs.push({ 
           row: headers.map(header => row[header]), 
           error: `Invalid date value for field: ${field.header}` 
         });
         leadFailureCount++;
         customFieldsFlag = true;
         continue;
       }
     } else if (field.type === 'Number') {
       if (isNaN(fieldValue)) {
         console.error(`Invalid number value for field: ${field.header}`);
         leadFailedLogs.push({ 
           row: headers.map(header => row[header]), 
           error: `Invalid number value for field: ${field.header}` 
         });
         leadFailureCount++;
         customFieldsFlag = true;
         continue;
       }
     } else if (field.type === 'Dropdown') {
       if (!field.dropdown.includes(fieldValue)) {
         console.error(`Invalid dropdown value for field: ${field.header}`);
         leadFailedLogs.push({ 
           row: headers.map(header => row[header]), 
           error: `Invalid dropdown value for field: ${field.header}` 
         });
         leadFailureCount++;
         customFieldsFlag = true;
         continue;
       }
     }
   }
 }
 
 if (customFieldsFlag) {
   continue;
 }
 
 // check if lead already exist for this contact id'

 // declare new lead variable outside if statement to avoid not defined issue of block scope
 let newLead;

 const { data: existingLead, error: fetchLeadError } = await supabase
           .from('crm_leads')
           .select('*')
           .eq('contact_id', contactId);

           console.log('existingLead : ',existingLead);
           
           if(existingLead.length>0){
            console.log('latest salesperson crm id : ',existingLead[existingLead.length-1].assigned_to);

              // For Duplicate leads, apply duplicate lead check.
           if(reassign=='skip'){
            lead_skip_count++;
            
            lead_skipped_Logs.push({ row: headers.map(header => row[header]), error: 'Lead Already Exist' });
            continue;
           }else
           if(reassign=='reassign'){
            // Assign Lead to latest salesperson
const leadData = {
  contact_id: contactId,
description: row.description,
requirement: row.requirement,
amount: row.amount ? parseFloat(row.amount) : null,
source: row.source,
title: row.title,
close_date: row.close_date || null,
created_by: imported_by,
assigned_by: imported_by,
stage: stageId,
workspace_id: workspaceId,
assigned_to: existingLead[existingLead.length-1].assigned_to,
last_update_at: new Date().toISOString(),
bulk_upload : 'Yes'

};

const { data, error: leadError } = await supabase
  .from('crm_leads')
  .insert(leadData)
  .select('id')
  .single();

if (leadError) {
  console.log('leadError : ',leadError);
  leadFailureCount++;
  leadFailedLogs.push({ row: headers.map(header => row[header]), error: leadError.message });
  continue;
} else {
  leadSuccessCount++;
  newLead = data;
  lead_ids_array.push(newLead.id);
}


           }
           

           }
            if(reassign=='duplicate' || existingLead.length==0){
// Insert lead data
const leadData = {
  contact_id: contactId,
description: row.description,
requirement: row.requirement,
amount: row.amount ? parseFloat(row.amount) : null,
source: row.source,
title: row.title,
close_date: row.close_date || null,
created_by: imported_by,
assigned_by: imported_by,
stage: stageId,
workspace_id: workspaceId,
assigned_to: assignedToId,
last_update_at: new Date().toISOString(),
bulk_upload : 'Yes'

};

const { data, error: leadError } = await supabase
  .from('crm_leads')
  .insert(leadData)
  .select('id')
  .single();

if (leadError) {
  console.log('leadError : ',leadError);
  leadFailureCount++;
  leadFailedLogs.push({ row: headers.map(header => row[header]), error: leadError.message });
  continue;
} else {
  newLead = data;
  lead_ids_array.push(newLead.id);
  leadSuccessCount++;
}
           }


         
 
         // Add data into lead custom fields table
 
         
 if (availableCustomFields.length > 0) {
   for (const field of availableCustomFields) {
     const fieldValue = row[field.header] || null;
     
     if(fieldValue !== null && fieldValue !== undefined && fieldValue !== ''){
       const { error: custom_field_values_error } = await supabase
       .from('crm_leads_custom_fields_values')
       .insert({
         workspace_id: workspaceId,
         lead_id: newLead.id,
         field_id: field.id,
         field_value: fieldValue
       });
       if (custom_field_values_error) {
         console.error('Failed to add values record', custom_field_values_error);
       }
     }
     }
     
     
 
    
 }
  
       } catch (error) {
                       console.error('Error processing row:', error);
                       leadFailedLogs.push({ row: headers.map(header => row[header]), error: error.message });
                     }
       }
 
       
     let formatted_contact_failed_logs,formatted_lead_failed_logs,formatted_company_failed_logs;
     let formatted_contact_skipped_Logs, formatted_lead_skipped_Logs, formatted_company_skipped_Logs;
 
     if(contactFailedLogs.length>0){
       formatted_contact_failed_logs = {
         head: headers,
         data: contactFailedLogs 
       };
     }else{
       formatted_contact_failed_logs = {};
     }
 
     if(leadFailedLogs.length>0){
       formatted_lead_failed_logs = {
         head: headers,
         data: leadFailedLogs 
       };
     }else{
       formatted_lead_failed_logs = {};
     }
 
     if(companyFailedLogs.length>0){
       formatted_company_failed_logs = {
         head: headers,
         data: companyFailedLogs 
       };
     }else{
       formatted_company_failed_logs = {};
     }
 
     if(contact_skipped_Logs.length>0){
       formatted_contact_skipped_Logs = {
         head: headers,
         data: contact_skipped_Logs 
       };
     }else{
       formatted_contact_skipped_Logs = {};
     }
 
     if(company_skipped_Logs.length>0){
       formatted_company_skipped_Logs = {
         head: headers,
         data: company_skipped_Logs 
       };
     }else{
       formatted_company_skipped_Logs = {};
     } 
 
     if(lead_skipped_Logs.length>0){
       formatted_lead_skipped_Logs = {
         head: headers,
         data: company_skipped_Logs 
       };
     }else{
       formatted_lead_skipped_Logs = {};
     } 
     
     const import_id = await logImportHistory({ source: fileName, module: 'Leads', added: leadSuccessCount, skipped: 0, imported_by:imported_by,workspace_id : workspaceId,failed_logs :formatted_lead_failed_logs,failed : leadFailureCount,skipped_logs : formatted_lead_skipped_Logs,successful_ids_array : lead_ids_array,parent_import_id : null});
     await logImportHistory({ source: fileName, module: 'Companies', added: companySuccessCount, skipped: companySkipCount, imported_by:imported_by,workspace_id : workspaceId,failed_logs :formatted_company_failed_logs,failed : companyFailureCount,skipped_logs : formatted_company_skipped_Logs,successful_ids_array : company_ids_array,parent_import_id :import_id});
     await logImportHistory({ source: fileName, module: 'Contacts', added: contactSuccessCount, skipped: contactSkipCount,imported_by:imported_by,workspace_id : workspaceId,failed_logs :formatted_contact_failed_logs,failed : contactFailureCount,skipped_logs : formatted_contact_skipped_Logs,successful_ids_array : contact_ids_array,parent_import_id :import_id});
     
      //  console.log('leadSuccessCount : ',leadSuccessCount)
      //  console.log('leadFailureCount : ',leadFailureCount)
      //  console.log('contactSuccessCount : ',contactSuccessCount)
      //  console.log('contactFailureCount : ',contactFailureCount)
      //  console.log('companySuccessCount : ',companySuccessCount)
      //  console.log('companyFailureCount : ',companyFailureCount)
       
       res.status(200).json({
         success: true,
         message: 'Import process completed',
         leadSuccessCount,
         lead_skip_count,
         leadFailureCount,
         contactSuccessCount,
         contactSkipCount,
         contactFailureCount,
         companySuccessCount,
         companySkipCount,
         companyFailureCount
       });
 
     });
   
    
 
 });

  // send bulk whatsapp from admin
  
  router.post("/send-bulk-whatsapp-inAdmin",  async (req, res) => {
  
    try {
      const { workspace_ids, template_name,header_type,attachment_url,tags_array, user_id } = req.body;
        console.log( workspace_ids, template_name,header_type,attachment_url,tags_array, user_id)
      if(!workspace_ids || !template_name){
        return res.status(400).json({ error: 'Missing workspace_id or template_name.' });
      }
  
      if (workspace_ids && workspace_ids.length > 0) {
        for (const workspace_id of workspace_ids) {
          const ownerWorkspaceRecord = await getValuefromdatabase(
            "workspace_details",
            "id",
            workspace_id
          );
  
          if (ownerWorkspaceRecord) {
           
  
            if(tags_array && tags_array.length>0){
              for (var i=0;i<tags_array.length;i++){
                if(ownerWorkspaceRecord.hasOwnProperty(tags_array[i])){
                  console.log(ownerWorkspaceRecord[tags_array[i]])
                  tags_array[i] = ownerWorkspaceRecord[tags_array[i]];
                }
              }
        
              console.log("tags after : ",tags_array);
             }
             if (header_type && header_type!='') {
              sentStatus =  await sendDynamicMessage(
                `${ownerWorkspaceRecord.owner_phone}`,
                 template_name,
                 "Y0CAnYCB0YZxGWABz2ifZjox461TIDThFWj0f0ojDo10sJ9YsMMgn7hRhjgRYFE5eqd7csc5PzMBQNuNxlGX7XgjiOMn4KbXPfqrk3Dcrao0NM7puLI9xeXKg94",
        "538489222672252",
         "6747fbfb0bfa5c6520c94c89",
          { [String(header_type)]: attachment_url },
          tags_array,
            '7',
        "669f54b69da4c3d727df4b2f")
            } else {
              sentStatus =   await sendDynamicMessage(
                `${ownerWorkspaceRecord.owner_phone}`,
                 template_name,
                 "Y0CAnYCB0YZxGWABz2ifZjox461TIDThFWj0f0ojDo10sJ9YsMMgn7hRhjgRYFE5eqd7csc5PzMBQNuNxlGX7XgjiOMn4KbXPfqrk3Dcrao0NM7puLI9xeXKg94",
        "538489222672252",
         "499886329872792",
          {},
          tags_array, '7',
        "6747fbfb0bfa5c6520c94c89")
            }
            console.log('sentStatus : ',sentStatus);
          }
  
          const wa_log = {
            workspace_id: workspace_id,
            sent_by : user_id ,
            status: sentStatus,
            
            template_name : template_name
            
            
          };
      
          const { data : newWaRecord,error: mailLogError } = await supabase
            .from('admin_whatsapp_logs')
            .insert(wa_log);
        }
      }
      
      res.status(200).send("Whatsapp sent successfully");
    } catch (error) {
      console.error("Error sending whatsapp:", error);
      res.status(500).send(`Internal Server Error. : ${error}`);
    }
  });


// send any email directly

router.post("/send-general-email", uploadToDisk.array("attachments"), async (req, res) => {
  try {
    console.log("send-general-email");

    const { user_id, to, subject, html, cc, bcc,event_name } = req.body;
    const attachments = req.files;
    // console.log("Received files:", req.files);
    // console.log("Received body:", req.body);
    if (!user_id || !to || !subject || !html ) {
      throw new Error(
        "user_id, to, subject and html are mandatory. Please provide all the mandatory fields"
      );
    }

    const { data: credentials, error: apiKeyError } = await supabase
      .from("workspace_email_credentials")
      .select("*")
      .eq("user_id", user_id)
      .single();

    if (apiKeyError || !credentials) {
      console.log("Error in Fetching credentials");
      console.log(apiKeyError);
      return res.status(401).json({ error: "Invalid or inactive API key" });
    }

    const { data: userProfile, error: userProfileError } = await supabase
      .from("user_profile")
      .select("*")
      .eq("id", user_id)
      .single();

    if (userProfileError || !userProfile) {
      console.log(userProfileError);
      return res
        .status(400)
        .json({ error: "User not found or error in fetching user profile" });
    }

    const workspaceId = userProfile.workspace_id;

    // let tags = await fetchAndMergeData(leadId, workspaceId);
    // const processedHtml = replaceTags(html, tags);
    // const processedSubject = replaceTags(subject, tags);

    // console.log("processed html is : ", processedHtml);

    const REFRESH_TOKEN = credentials.refresh_token;
    const senderMail = credentials.sender_mail_id;
    const senderName = `${userProfile.first_name} ${userProfile.last_name}`;
    console.log('Sender Name - ', senderName);
    const mail_client = credentials.email_client;

    if(mail_client=='gmail'){
      emailInfo = await sendEmail({
        REFRESH_TOKEN,
        senderMail,
        senderName,
        to,
        processedSubject: subject,  
      processedHtml: html, 
        cc,
        bcc,
        attachments,
      });
    }else
    if(mail_client=='outlook'){
   
      emailInfo = await sendOutlookEmail({
        REFRESH_TOKEN,
       // senderMail,
       // senderName,
        to,
        subject : subject,
        htmlBody : html,
        cc,
        bcc,
        attachments,
      });
    }

    await supabase.from("email_log").insert({
      event: event_name,
      sent_to: to,
      workspace_id: workspaceId,
      sending_email: senderMail,
      cc: cc,
      bcc: bcc,
      subject: subject,
      lead_id: null,
      user_id: user_id,
    });
    res.send(info);
  } catch (error) {
    console.error("Error in /send-general-email route:", error);
    res.status(500).json({ error: error.message, stack: error.stack });
  }
});


  
   // upload product
   router.post('/upload-product', uploadToMemory.single('csvFile'), async (req, res) => {
    try {
  
      
      const workspaceId = Number(req.body.workspace_id);
      const imported_by = req.body.imported_by;
      let catId, unitId;
  
      // Define interfaces
      /**
       * @typedef {Object} FailedLog
       * @property {any} row - The data row
       * @property {string} error - Error description
       */
  
      /** @type {FailedLog[]} */
      let productFailedLogs = [];
      /** @type {FailedLog[]} */
      let productSkippedLogs = [];
      let successful_product_array = [];
  
      // Validate required parameters
      if (!workspaceId || !imported_by) {
        return res.status(400).json({ error: 'Missing workspace_id or imported_by' });
      }
  
      // Check if file was provided
      if (!req.file) {
        return res.status(400).json({ error: 'Missing file' });
      }
  
      const fileName = req.file.originalname;
      console.log('CSV file name:', fileName);
  
      // Parse CSV
      const rows = [];
      const headers = [];
      let isFirstRow = true;
  
     // Parse CSV content from in-memory buffer
     const bufferStream = require('stream').Readable.from(req.file.buffer); // Convert buffer to readable stream
   
  
  
      bufferStream
       .pipe(csv())
       .on('headers', (headerRow) => {
          headers.push(...headerRow);
        })
        .on('data', (row) => {
          rows.push(row);
        })
        .on('end', async () => {
          // Process the CSV data after it's fully loaded
          const dataRows = rows;
  
          try {
            let productSuccessCount = 0, productFailureCount = 0, productSkipCount = 0;
  
            // Check required columns
            const requiredColumns = ['product_name', 'category_name', 'unit'];
            const missingColumns = requiredColumns.filter(col => !headers.includes(col));
            
            if (missingColumns.length > 0) {
              console.log(`Missing required columns: ${missingColumns.join(', ')}`);
              productFailedLogs.push({ row: dataRows, error: `Missing required columns: ${missingColumns.join(', ')}` });
              
              const formatted_lead_failed_logs = {
                head: headers,
                data: productFailedLogs
              };
              
              await log_product_import({
                source: fileName,
                module: 'Products',
                added: 0,
                skipped: 0,
                imported_by: imported_by,
                workspace_id: workspaceId,
                failed_logs: formatted_lead_failed_logs,
                failed: dataRows.length,
                skipped_logs: null
              });
              
              return res.status(400).json({ error: `Missing required columns: ${missingColumns.join(', ')}` });
            }
  
            // Create column indices for easy access
            const columnIndices = {};
            headers.forEach((header, index) => {
              columnIndices[header] = header;
            });
  
            // Fetch All custom Fields
            const { data: allCustomFields, error: fetchCustomFieldError } = await supabase
              .from('crm_product_custom_fileds_metadata')
              .select('*')
              .eq('workspace_id', workspaceId);
  
            console.log('allCustomFields : ', allCustomFields);
  
            // Function to find matching field IDs
            const findMatchingFieldIds = (headers, allCustomFields) => {
              return headers.reduce((acc, header) => {
                const match = allCustomFields.find(field => field.name.toLowerCase() === header.toLowerCase());
                if (match) acc.push({ header, id: match.id });
                return acc;
              }, []);
            };
  
            const availableCustomFields = findMatchingFieldIds(headers, allCustomFields);
           // console.log('availableCustomFields :', availableCustomFields);
          //  console.log('dataRows : ', dataRows);
  
            // Process each row
            for (const row of dataRows) {
              let customFieldsFlag = false;
              
              // Validate custom fields
              if (availableCustomFields.length > 0) {
                for (const field of availableCustomFields) {
                  const fieldValue = row[field.header] || null;
  
                  // Validate value based on the type
                  if (field.type === 'Date') {
                    if (fieldValue && isNaN(Date.parse(fieldValue))) {
                      console.error(`Invalid date value for field: ${field.header}`);
                      productFailedLogs.push({ row, error: `Invalid date value for field: ${field.header}` });
                      productFailureCount++;
                      customFieldsFlag = true;
                      continue;
                    }
                  } else if (field.type === 'Number') {
                    if (isNaN(fieldValue)) {
                      console.error(`Invalid number value for field: ${field.header}`);
                      productFailedLogs.push({ row, error: `Invalid number value for field: ${field.header}` });
                      productFailureCount++;
                      customFieldsFlag = true;
                      continue;
                    }
                  } else if (field.type === 'Dropdown') {
                    if (!field.dropdown.includes(fieldValue)) {
                      console.error(`Invalid dropdown value for field: ${field.header}`);
                      productFailedLogs.push({ row, error: `Invalid dropdown value for field: ${field.header}` });
                      productFailureCount++;
                      customFieldsFlag = true;
                      continue;
                    }
                  }
                }
              }
  
              if (customFieldsFlag) {
                continue;
              }
  
              // Check if product already exists
              const { data: existingProduct, error: fetchProductError } = await supabase
                .from('crm_products')
                .select('id')
                .eq('product_code', row[columnIndices.product_code])
                .eq('workspace_id', workspaceId)
                .single();
  
              if (existingProduct) {
                productSkipCount++;
                productSkippedLogs.push({ 
                  row, 
                  error: `Product ${row[columnIndices.product_name]} already exists with same product_code.` 
                });
                console.log(`Product ${row[columnIndices.product_name]} already exists. Skipping this record.`);
                continue;
              }
  
              // Check if category already exists or create new
              const { data: existingCategory, error: fetchCatError } = await supabase
                .from('crm_product_category')
                .select('*')
                .eq('name', row[columnIndices.category_name])
                .eq('workspace_id', workspaceId)
                .single();
  
              if (!existingCategory) {
                let categoryData = {
                  name: row[columnIndices.category_name],
                  workspace_id: workspaceId
                };
                
                const { data: newCat, error: newCatError } = await supabase
                  .from('crm_product_category')
                  .insert(categoryData)
                  .select('id')
                  .single();
  
                catId = newCat.id;
              } else {
                catId = existingCategory.id;
              }
  
              console.log(catId);
  
              // Check if unit already exists
              const { data: existingUnit, error: fetchUnitError } = await supabase
                .from('crm_units')
                .select('*')
                .eq('unit', row[columnIndices.unit])
                .eq('workspace_id', workspaceId)
                .single();
  
              if (!existingUnit || fetchUnitError) {
                productFailureCount++;
                productFailedLogs.push({ row, error: `Unit ${row[columnIndices.unit]} not exists.` });
                console.log(`Unit ${row[columnIndices.unit]} not exists. Skipping this record.`);
                continue;
              } else {
                unitId = existingUnit.id;
              }
  
              console.log(unitId);
  
              // Insert data into crm_products table
              const productData = {
                product_name: row[columnIndices.product_name],
                category_id: catId,
                unit_id: unitId,
                price: row[columnIndices.price] || null,
                is_active: true,
                product_code: row[columnIndices.product_code] || null,
                description: row[columnIndices.description] || null,
                image_url: row[columnIndices.image_url],
                max_discount: row[columnIndices.max_discount] || null,
                hsn_code: row[columnIndices.hsn_code] || null,
                workspace_id: workspaceId,
              };
  
              const { data: newProduct, error: newProductError } = await supabase
                .from('crm_products')
                .insert(productData)
                .select('id')
                .single();
  
              if (newProductError) {
                console.error('Failed to add product record', newProductError);
                productFailedLogs.push({ row, error: newProductError });
                productFailureCount++;
                continue;
              } else {
                productSuccessCount++;
                successful_product_array.push(newProduct.id);
              }
  
              // Add data into custom fields table
              if (availableCustomFields.length > 0) {
                for (const field of availableCustomFields) {
                  const fieldValue = row[field.header] || null;
  
                  // Check if the record exists
                  const { data: existingRecord, error: fetchError } = await supabase
                    .from('crm_products_custom_fields_values')
                    .select('id')
                    .eq('workspace_id', workspaceId)
                    .eq('product_id', newProduct.id)
                    .eq('field_id', field.id)
                    .single();
  
                  if (fetchError && fetchError.code !== 'PGRST116') {
                    console.error('Error checking existing record:', fetchError);
                    return res.status(500).json({ error: 'Failed to check existing record' });
                  }
  
                  if (existingRecord) {
                    // Update the existing record
                    const { error: updateError } = await supabase
                      .from('crm_products_custom_fields_values')
                      .update({ field_value: fieldValue })
                      .eq('id', existingRecord.id);
  
                    if (updateError) {
                      console.error('Failed to update record', updateError);
                      return res.status(500).json({ error: 'Failed to update record' });
                    }
                  } else {
                    const { error: custom_field_values_error } = await supabase
                      .from('crm_products_custom_fields_values')
                      .insert({
                        workspace_id: workspaceId,
                        product_id: newProduct.id,
                        field_id: field.id,
                        field_value: fieldValue
                      });
  
                    if (custom_field_values_error) {
                      console.error('Failed to add values record', custom_field_values_error);
                    }
                  }
                }
              }
            }
  
            // Prepare logs for history
            let formatted_product_skipped_logs;
            const formatted_lead_failed_logs = {
              head: headers,
              data: productFailedLogs
            };
  
            if (productSkippedLogs.length > 0) {
              formatted_product_skipped_logs = {
                head: headers,
                data: productSkippedLogs
              };
            } else {
              formatted_product_skipped_logs = {};
            }
  
            // Log import history
            await log_product_import({
              source: fileName,
              module: 'Products',
              added: productSuccessCount,
              skipped: productSkipCount,
              imported_by: imported_by,
              workspace_id: workspaceId,
              failed_logs: formatted_lead_failed_logs,
              failed: productFailureCount,
              skipped_logs: formatted_product_skipped_logs,
              successful_ids_array : successful_product_array
            });
  
            
            // Send success response
            const responseMessage = `Success! Product imported: ${productSuccessCount}, Failed: ${productFailureCount} Skipped: ${productSkipCount}`;
            console.log(responseMessage);
           
            res.status(200).json({ message: responseMessage });
          } catch (error) {
            console.error('Failed to process CSV data', error);
            res.status(500).json({ error: 'Failed to process CSV data' });
          }
        })
        .on('error', (error) => {
          console.error('Error parsing CSV:', error);
          res.status(500).json({ error: 'Failed to parse CSV file' });
        });
    } catch (error) {
      console.error('Failed to insert CSV data', error);
      res.status(500).json({ error: 'Failed to insert CSV data' });
    }
  });



router.post("/send_bulk_renewal_reminder",  async (req, res) => {
  try {
    const { workspace_ids, subject, email_body, cc, attachments, user_id,expired_days,module_id } = req.body;

    if (workspace_ids && workspace_ids.length > 0) {
      for (const workspace_id of workspace_ids) {
       
        
const { data : ownerWorkspaceRecord, error } = await supabase
.rpc('get_workspace_app_data', {
p_app_id : module_id, 
p_workspace_id : workspace_id
})

// if (error) console.error(error)
// else console.log(data)

        ownerWorkspaceRecord["days"] = expired_days;
        if (ownerWorkspaceRecord) {
          // Create a regex pattern for all placeholders
          const variableKeys = Object.keys(ownerWorkspaceRecord);
          const placeholderRegex = new RegExp(
            variableKeys.map((key) => `{{${key}}}`).join("|"),
            "g"
          );

          // Replace placeholders dynamically
          const replaceVariables = (text) =>
            text.replace(placeholderRegex, (match) => {
              const key = match.slice(2, -2); // Extract variable name from {{variable}}
              return ownerWorkspaceRecord[key] || "";
            });

          const personalizedSubject = replaceVariables(subject);
          const personalizedBody = replaceVariables(email_body);

          // Process attachments
          const attachmentUrls = Array.isArray(attachments) ? attachments : [];
          const allAttachments = await getAttachments(attachmentUrls, req.files);

          // Send email
          await sendAdminEmail(
             ownerWorkspaceRecord.owner_email,
            personalizedSubject,
            "",
            personalizedBody,
            cc,
            allAttachments,
            workspace_id,
            user_id
          );
        }
      }
    }

    res.status(200).send("Emails sent successfully");
  } catch (error) {
    console.error("Error sending emails:", error);
    res.status(500).send("Internal Server Error.");
  }
});

router.post("/send_renewal_reminder",async (req, res) => {
  try {
    // Call the database function to get templates and workspaces
    const { data, error } = await supabase.rpc('get_workspaces_for_renewal_reminder_emails');
    
    if (error) {
      console.error('Error fetching renewal reminder data:', error);
      return res.status(500).json({ error: 'Failed to fetch reminder data' });
    }
    
    if (!data || Object.keys(data).length === 0) {
      console.log('No reminders to send today');
      return res.status(200).json({ message: 'No reminders to send today' });
    }
    
    console.log('Templates to process:', Object.keys(data).length);
    
    // Track results for reporting
    const results = {
      success: [],
      failed: []
    };
    
    // Process each template
    for (const templateId in data) {
      const templateData = data[templateId];
      const { workspace_ids, subject, email_body,expire_days, attachments,module_id } = templateData;
      let expired_days = (expire_days > 0) ? expire_days : expire_days*(-1);
      if (!workspace_ids || workspace_ids.length === 0) {
        console.log(`No workspaces for template ID ${templateId}, skipping`);
        continue;
      }
      
      try {
        // Call your existing API endpoint with the data from the function
        const response = await axios.post(
          "https://backend.automatebusiness.com/api/send_bulk_renewal_reminder", // Adjust API URL if needed
          {
            workspace_ids,
            subject,
            email_body,
            attachments: attachments || [],
            cc: "", 
            user_id: "20ee8873-63c5-4f32-872d-f1b200bf4889",
            expired_days,
            module_id 
          }
        );
        
        console.log(`Template ${templateId}: Successfully sent emails to ${workspace_ids.length} workspaces`);
        results.success.push({
          templateId,
          workspaceCount: workspace_ids.length
        });
        
      } catch (templateError) {
        console.error(`Failed to process template ${templateId}:`, templateError);
        results.failed.push({
          templateId,
          workspaceCount: workspace_ids.length,
          error: templateError.message
        });
      }
    }
    return;
    // Return results
    /*
    return res.status(200).json({
      message: 'Renewal reminder process completed',
      results: {
        successCount: results.success.length,
        failureCount: results.failed.length,
        details: results
      }
    }); */
    
  } catch (error) {
    console.error('Error in send_renewal_reminder:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
});

// upload bulk contact


router.post('/upload-contacts', uploadToMemory.single('csvFile'), async (req, res) => {
  console.log('request received at upload contacts');
  try {
    const workspaceId = Number(req.body.workspace_id);
    const imported_by = req.body.imported_by;
    let contactId = '', companyId;

    // Initialize tracking variables
    let contactFailedLogs = [], companyFailedLogs = [];
    let contact_skipped_Logs = [], company_skipped_Logs = [];
    let contactSuccessCount = 0, contactFailureCount = 0, contactSkipCount = 0;
    let companySuccessCount = 0, companyFailureCount = 0, companySkipCount = 0;
    let contact_ids_array = [], company_ids_array = [];

    // Validate required parameters
    if (!workspaceId || !imported_by) {
      return res.status(400).json({ error: 'Missing workspace_id or imported_by' });
    }

    // Validate file
    if (!req.file) {
      return res.status(400).json({ error: 'Missing file' });
    }

    const fileName = req.file.originalname;

    // Validate file type
    if (!fileName.toLowerCase().endsWith('.csv')) {
      return res.status(400).json({ error: 'Invalid file format. Only CSV files are allowed.' });
    }

    // Parse CSV
    const rows = [];
    const headers = [];

    const bufferStream = require('stream').Readable.from(req.file.buffer);

    bufferStream
      .pipe(csv())
      .on('headers', (headerRow) => {
        headers.push(...headerRow);
      })
      .on('data', (row) => {
        rows.push(row);
      })
      .on('end', async () => {
        const dataRows = rows;

        try {
          // Validate required columns
          const requiredColumns = ['first_name', 'phone', 'country_code'];
          const missingColumns = requiredColumns.filter(col => !headers.includes(col));
          
          if (missingColumns.length > 0) {
            contactFailedLogs.push({ row: dataRows, error: `Missing required columns: ${missingColumns.join(', ')}` });
            const formatted_contact_failed_logs = {
              head: headers,
              data: contactFailedLogs
            };
            
            await logImportHistory({
              source: fileName,
              module: 'Contacts',
              added: 0,
              skipped: 0,
              imported_by: imported_by,
              workspace_id: workspaceId,
              failed_logs: formatted_contact_failed_logs,
              failed: dataRows.length,
              skipped_logs: {},
              successful_ids_array: []
            });
            
            return res.status(400).json({ error: `Missing required columns: ${missingColumns.join(', ')}` });
          }

          // Create column indices
          const columnIndices = {};
          headers.forEach((header) => {
            columnIndices[header] = header;
          });

          // Fetch All custom Fields
          const { data: allCustomFields, error: fetchCustomFieldError } = await supabase
            .from('crm_contacts_custom_fields_metadata')
            .select('*')
            .eq('workspace_id', workspaceId);

          // Function to find matching field IDs
          const findMatchingFieldIds = (headers, allCustomFields) => {
            return headers.reduce((acc, header) => {
              const match = allCustomFields.find(field => 
                field.name.toLowerCase() === header.toLowerCase()
              );
              if (match) acc.push({ header, id: match.id });
              return acc;
            }, []);
          };

          const availableCustomFields = findMatchingFieldIds(headers, allCustomFields);

          // Process each row
          for (const row of dataRows) {
            try {
              // Validate required fields
              if (!row[columnIndices.first_name] || !row[columnIndices.country_code] || !row[columnIndices.phone]) {
                contactFailureCount++;
                contactFailedLogs.push({ 
                  row: headers.map(header => row[header]), 
                  error: 'Either first name, phone or country code is missing.' 
                });
                continue;
              }

              // Sanitize phone number
              const sanitizedPhone = row[columnIndices.phone].replace(/\D/g, '');
              const sanitizedEmail = row[columnIndices.email] ? String(row[columnIndices.email]).trim().toLowerCase() : null;

              // Process company if exists
              if (row[columnIndices.company_name]) {
                const { data: existingCompany, error: fetchCompanyError } = await supabase
                  .from('crm_companies')
                  .select('*')
                  .eq('name', row[columnIndices.company_name])
                  .eq('workspace_id', workspaceId)
                  .single();

                if (existingCompany) {
                  companySkipCount++;
                  companyId = existingCompany.id;
                  company_skipped_Logs.push({
                    row: headers.map(header => row[header]),
                    error: 'Company already exists'
                  });
                } else {
                  const companyData = {
                    name: row[columnIndices.company_name],
                    workspace_id: workspaceId,
                    website: row[columnIndices.website] || null,
                    tax_no: row[columnIndices.tax_no] || null,
                    address: row[columnIndices.company_billing_address] || null,
                    shipping_address: row[columnIndices.company_shipping_address] || null,
                    city : row[columnIndices.company_city] || null,
                    state : row[columnIndices.company_state] || null,
                    pincode : row[columnIndices.company_pincode] || null,
                    country : row[columnIndices.company_country] || null,
                  };

                  const { data: newCompany, error: companyError } = await supabase
                    .from('crm_companies')
                    .insert(companyData)
                    .select('id')
                    .single();

                  if (companyError) {
                    companyFailureCount++;
                    companyFailedLogs.push({ 
                      row: headers.map(header => row[header]), 
                      error: companyError.message 
                    });
                    continue;
                  }

                  companyId = newCompany.id;
                  companySuccessCount++;
                  company_ids_array.push(companyId);
                }
              } else {
                companyId = null;
              }

              // Check for duplicate contact using both email and phone
              const { data: existingContacts, error: fetchContactError } = await supabase
                .from('crm_contacts')
                .select('*')
                .eq('workspace_id', workspaceId)
                .or(`phone.eq.${sanitizedPhone}${sanitizedEmail ? `,email.eq.${sanitizedEmail}` : ''}`);

              if (existingContacts && existingContacts.length > 0) {
                contactSkipCount++;
                contactId = existingContacts[0].id;
                contact_skipped_Logs.push({
                  row: headers.map(header => row[header]),
                  error: 'Contact already exists with same email or phone'
                });
              } else {
                const contactData = {
                  first_name: row[columnIndices.first_name] || null,
                  last_name: row[columnIndices.last_name] || null,
                  email: sanitizedEmail,
                  phone: sanitizedPhone,
                  address: row[columnIndices.address] || null,
                  country: row[columnIndices.country] || null,
                  country_code: row[columnIndices.country_code] || '91',
                  city: row[columnIndices.city] || null,
                  state: row[columnIndices.state] || null,
                  pincode: row[columnIndices.pincode] || null,
                  designation: row[columnIndices.designation] || null,
                  dob: row[columnIndices.date_of_birth] || null,
                  doa: row[columnIndices.date_of_anniversary] || null,
                  workspace_id: workspaceId,
                  company_id: companyId,
                  is_active: true,
                  image_url: '',
                  bulk_upload : 'Yes'
                };

                const { data: newContact, error: contactError } = await supabase
                  .from('crm_contacts')
                  .insert(contactData)
                  .select('id')
                  .single();

                if (contactError) {
                  contactFailureCount++;
                  contactFailedLogs.push({ 
                    row: headers.map(header => row[header]), 
                    error: contactError.message 
                  });
                  continue;
                }

                contactId = newContact.id;
                contactSuccessCount++;
                contact_ids_array.push(contactId);

                // Process custom fields
                if (availableCustomFields.length > 0) {
                  for (const field of availableCustomFields) {
                    const fieldValue = row[columnIndices[field.header]];
                    if (fieldValue) {
                      const { error: custom_field_values_error } = await supabase
                        .from('crm_contacts_custom_fields_values')
                        .insert({
                          workspace_id: workspaceId,
                          contact_id: contactId,
                          field_id: field.id,
                          field_value: fieldValue
                        });

                      if (custom_field_values_error) {
                        console.error('Failed to add custom field value', custom_field_values_error);
                      }
                    }
                  }
                }
              }
            } catch (error) {
              console.error('Error processing row:', error);
              contactFailureCount++;
              contactFailedLogs.push({ 
                row: headers.map(header => row[header]), 
                error: error.message 
              });
            }
          }

          // Prepare formatted logs
          const formatted_contact_failed_logs = contactFailedLogs.length > 0 
            ? { head: headers, data: contactFailedLogs }
            : {};

          const formatted_company_failed_logs = companyFailedLogs.length > 0
            ? { head: headers, data: companyFailedLogs }
            : {};

          const formatted_contact_skipped_logs = contact_skipped_Logs.length > 0
            ? { head: headers, data: contact_skipped_Logs }
            : {};

          const formatted_company_skipped_logs = company_skipped_Logs.length > 0
            ? { head: headers, data: company_skipped_Logs }
            : {};

          // Log import history
          await logImportHistory({
            source: fileName,
            module: 'Contacts',
            added: contactSuccessCount,
            skipped: contactSkipCount,
            imported_by: imported_by,
            workspace_id: workspaceId,
            failed_logs: formatted_contact_failed_logs,
            failed: contactFailureCount,
            skipped_logs: formatted_contact_skipped_logs,
            successful_ids_array: contact_ids_array
          });

          await logImportHistory({
            source: fileName,
            module: 'Companies',
            added: companySuccessCount,
            skipped: companySkipCount,
            imported_by: imported_by,
            workspace_id: workspaceId,
            failed_logs: formatted_company_failed_logs,
            failed: companyFailureCount,
            skipped_logs: formatted_company_skipped_logs,
            successful_ids_array: company_ids_array
          });

          res.status(200).json({
            success: true,
            message: 'Import process completed',
            Contacts_imported: contactSuccessCount,
            Failed: contactFailureCount,
            Skipped: contactSkipCount,
            Companies_Imported: companySuccessCount,
            Companies_Failed: companyFailureCount,
            Companies_Skipped: companySkipCount
          });

        } catch (error) {
          console.error('Failed to process CSV data', error);
          res.status(500).json({ error: 'Failed to process CSV data' });
        }
      })
      .on('error', (error) => {
        console.error('Error parsing CSV:', error);
        res.status(500).json({ error: 'Failed to parse CSV file' });
      });

  } catch (error) {
    console.error('Failed to insert CSV data', error);
    res.status(500).json({ error: 'Failed to insert CSV data' });
  }
});



// send bulk email in admin using auth
router.post("/send_bulk_email_with_auth", uploadToDisk.array("files"), async (req, res) => {
  try {
    const { workspace_ids, subject, email_body, cc, attachments,user_id } = req.body;

    const { data: credentials, error: apiKeyError } = await supabase
        .from("workspace_email_credentials")
        .select("*")
        .eq("user_id", user_id)
        .single();
  
      if (apiKeyError || !credentials) {
        console.log("Error in Fetching credentials");
        console.log(apiKeyError);
        return res.status(401).json({ error: "Invalid or inactive API key" });
      }
     
      
      const { data: userProfile, error: userProfileError } = await supabase
        .from("user_profile")
        .select("*")
        .eq("id", user_id)
        .single();
  
      if (userProfileError || !userProfile) {
        console.log(userProfileError);
        return res
          .status(400)
          .json({ error: "User not found or error in fetching user profile" });
      }
  
      
  
      const REFRESH_TOKEN = credentials.refresh_token;
      const senderMail = credentials.sender_mail_id;
      const senderName = `${userProfile.first_name} ${userProfile.last_name}`;
      console.log('Sender Name - ', senderName);
      
      
    if (workspace_ids && workspace_ids.length > 0) {
      for (const workspace_id of workspace_ids) {
        const ownerWorkspaceRecord = await getValuefromdatabase(
          "workspace_details",
          "id",
          workspace_id
        );

        

        if (ownerWorkspaceRecord) {
        
          let personalizedSubject = subject;
          let personalizedBody = email_body;

          // Replace placeholders with workspace-specific values
          Object.keys(ownerWorkspaceRecord).forEach((variable) => {
            const replacementValue = ownerWorkspaceRecord[variable] || "";
            
            personalizedSubject = personalizedSubject.replaceAll(
              `{{${variable}}}`,
              replacementValue
            );
            personalizedBody = personalizedBody.replaceAll(
              `{{${variable}}}`,
              replacementValue
            );
          });

          // Process attachments
          const attachmentUrls = Array.isArray(attachments) ? attachments : [];
          const allAttachments = await getAttachments(attachmentUrls, req.files);
   
      const info = await sendEmail({
        REFRESH_TOKEN,
        senderMail,
        senderName,
        to : ownerWorkspaceRecord.owner_email,
        processedSubject: personalizedSubject,  
        processedHtml: personalizedBody,        
        cc,
        bcc : '',
        attachments : allAttachments,
      });



    //  console.log("Email info:", info);

    const email_log = {
      workspace_id: workspace_id,
      sent_by : user_id ,
      status: info.response,
      cc: cc,
     
      attachments: attachments,
      subject : personalizedSubject,
      body :  personalizedBody
      
    };

    const { data : newMailRecord,error: mailLogError } = await supabase
      .from('admin_emails_log')
      .insert(email_log);

        }


      }
    }

    res.status(200).send("Emails sent successfully");
  } catch (error) {
    console.error("Error sending emails:", error);
    res.status(500).send("Internal Server Error.");
  }
});


// bulk upload lead using pipeline


router.post("/upload-lead-with-pipeline", uploadToMemory.single('csvFile'), async (req, res) => {
  // console.log(req);
   const workspaceId =  req.body.workspace_id;//Number(req.query.workspace_id);
   const imported_by = req.body.imported_by;//req.query.imported_by;
   const reassign = req.body.reassign;
   const pipeline_id = req.body.pipeline_id;
 
   console.log('workspaceId,imported_by,reassign : ',workspaceId,imported_by,reassign);
   if (!workspaceId || !imported_by || !reassign) {
     return res.status(400).json({ error: 'Missing workspace_id or imported_by or reassign settings' });
   }
 
   if (!req.file) {
     return res.status(400).json({ error: 'Missing CSV file' });
   }
 
   //const filePath = path.join(__dirname, req.file.path); // Path to the uploaded file
   const fileName = req.file.originalname; // Get the original file name
 
   console.log('fileName:', fileName); 
 
   const fileExtension = fileName.split('.').pop().toLowerCase();
   
   if (fileExtension !== 'csv') {
     return res.status(400).json({ error: 'Invalid file format. Only CSV files are allowed.' });
   }
   
   // Additionally, check MIME type if available
   if (req.file.mimetype && req.file.mimetype !== 'text/csv') {
     return res.status(400).json({ error: 'Invalid file type. Only CSV files are allowed.' });
   }
   // Parse CSV content
   const dataRows = [];
   const requiredColumns = ['first_name', 'email', 'phone', 'stage','assigned_to_email'];
   
   let headers = [];
     
 
   let contactFailedLogs = [], leadFailedLogs = [], companyFailedLogs = [];
   let contact_skipped_Logs = [], lead_skipped_Logs = [], company_skipped_Logs = [];
   
   let leadSuccessCount = 0,lead_skip_count = 0, leadFailureCount = 0;
   let contactSuccessCount = 0, contactFailureCount = 0, contactSkipCount = 0;
   let companySuccessCount = 0, companyFailureCount = 0, companySkipCount = 0;
   let formattedData = [];
   let lead_ids_array = [];
   let contact_ids_array = [];
   let company_ids_array = [];
         
  
 
   // Parse CSV content from in-memory buffer
 const bufferStream = require('stream').Readable.from(req.file.buffer); // Convert buffer to readable stream
 
 bufferStream
   .pipe(csv())
   .on('headers', (headerList) => {
     headers = headerList;
   })
   .on('data', (row) => {
     formattedData = headers.map(header => row[header]);
     dataRows.push(row);
   })
   
     .on('end', async () => {
        // console.log('Data Rows:', dataRows.length);
       const missingColumns = requiredColumns.filter(col => !headers.includes(col));
       if (missingColumns.length > 0) {
         
         leadFailedLogs.push({ row:formattedData, error: `Missing required columns: ${missingColumns.join(', ')}` });
     const  formatted_lead_failed_logs = {
         head: headers,
         data: leadFailedLogs 
       };
       await logImportHistory({ source: fileName, module: 'Leads', added: 0, skipped: 0, imported_by:imported_by,workspace_id : workspaceId,failed_logs :formatted_lead_failed_logs,failed : dataRows.length});
    
         return res.status(400).json({ error: `Missing required columns: ${missingColumns.join(', ')}` });
       }
 
       
       // Fetch custom fields
       const { data: allCustomFields, error: fetchCustomFieldError } = await supabase
         .from('crm_leads_custom_fields_metadata')
         .select('*')
         .eq('workspace_id', workspaceId)
         .eq('pipeline_id', pipeline_id) ;

       if (fetchCustomFieldError) {
         return res.status(500).json({ error: 'Error fetching custom fields' });
       }
       
       
 
       for (const row of dataRows) {
         try{
        // console.log('row : ' ,row)
         let stageId = '', assignedToId = '', companyId = '', contactId = '';
        // const pipeline_name = row.pipeLine_name; 
         const stage_name =  row.stage; 

         
         // Fetch stage
         const { data: existingStage, error: fetchStageError } = await supabase
           .from('crm_stages')
           .select('*')
           .eq('name', stage_name)
           .eq('pipeline', pipeline_id)
           .single();
 
         if (!existingStage || fetchStageError) {
           leadFailedLogs.push({ row: headers.map(header => row[header]), error: `Stage ${stage_name} not exists.` });
           leadFailureCount++;
           continue;
         } else {
           stageId = existingStage.id;
         }
         //console.log('pipeline_id',pipeline_id)
         // Fetch assigned user profile
         const { data: userProfile, error: userProfileError } = await supabase
           .from('user_profile')
           .select('id')
           .eq('email', row.assigned_to_email)
           .eq('workspace_id', workspaceId)
           .single();
 
         if (userProfileError || !userProfile) {
            // console.log('userProfileError',userProfileError);
           leadFailedLogs.push({ row: headers.map(header => row[header]), error: `Salesperson ${row.assigned_to_email} not exists.` });
           leadFailureCount++;
           continue;
         }
         const user_id = userProfile.id;
         //console.log('user_id',user_id)
         const { data: crmTeam, error: crmTeamError } = await supabase
           .from('crm_team')
           .select('id')
           .eq('user_id', user_id)
           .single();
 
         if (crmTeamError || !crmTeam) {
           leadFailedLogs.push({ row: headers.map(header => row[header]), error: `${row.assigned_to_email} could not be found in CRM Team.` });
           leadFailureCount++;
           continue;
         }
         assignedToId = crmTeam.id;
 
         //console.log('assignedToId : ',assignedToId);
 
         // Check if company exists or insert new company
         const company_name = row.company_name;
         if (company_name) {
           const { data: existingCompany, error: fetchCompanyError } = await supabase
             .from('crm_companies')
             .select('*')
             .eq('name', company_name)
             .eq('workspace_id', workspaceId)
             .single();
 
           if (existingCompany) {
             companySkipCount++;
             companyId = existingCompany.id;
             company_skipped_Logs.push({ row: headers.map(header => row[header]), error: `Company Already Exist with this name.` });
           
             
           } else {
             const companyData = {
               name: company_name,
               workspace_id: workspaceId,
               website: row.website,
               tax_no: row.tax_no,
               address: row[columnIndices.company_billing_address],
                    shipping_address: row[columnIndices.company_shipping_address],
                    city : row[columnIndices.company_city],
                    state : row[columnIndices.company_state],
                    pincode : row[columnIndices.company_pincode],
                    country : row[columnIndices.company_country],
             };
             const { data: newCompany, error: companyError } = await supabase
               .from('crm_companies')
               .insert(companyData)
               .select('id')
               .single();
 
             if (companyError) {
               companyFailureCount++;
               companyFailedLogs.push({ row: headers.map(header => row[header]), error: companyError.message });
               continue;
             }
             companyId = newCompany.id;
             console.log('companyId : ',companyId);
 
             companySuccessCount++;
             company_ids_array.push(newCompany.id);
           }
         } else {
           companyId = null;
         }
         console.log('companyId : 2 ',companyId);
         // Sanitize the phone number by removing spaces and non-numeric characters
         const sanitizedPhone = row.phone.replace(/\D/g, '');
         const sanitizedEmail = String(row.email).trim();
       
 
         // Check if contact exists or insert new contact
         const { data: existingContact, error: fetchContactError } = await supabase
         .from('crm_contacts')
         .select('*')
         .eq('phone', sanitizedPhone)
         .eq('email',sanitizedEmail )
         .eq('workspace_id', workspaceId)
         .single();

 console.log('existingContact : ',existingContact);

         if (existingContact) {
           contactSkipCount++;
           contactId = existingContact.id;
           contact_skipped_Logs.push({ row: headers.map(header => row[header]), error: 'Contact Already exist.' });
           
         } else {
           const contactData = {
             first_name: row.first_name,
             last_name: row.last_name,
             email: row.email,
             phone: sanitizedPhone || null,
             address: row[columnIndices.address] || null,
             country: row[columnIndices.country] || null,
             country_code: row[columnIndices.country_code] || '91',
             city: row[columnIndices.city] || null,
             state: row[columnIndices.state] || null,
             pincode: row[columnIndices.pincode] || null,
             designation: row[columnIndices.designation] || null,
             dob: row[columnIndices.date_of_birth] || null,
             doa: row[columnIndices.date_of_anniversary] || null,
             workspace_id: workspaceId,
             company_id: companyId,
             is_active: true,
             image_url: ''
             
           };
 
           const { data: newContact, error: contactError } = await supabase
             .from('crm_contacts')
             .insert(contactData)
             .select('id')
             .single();
 
           if (contactError) {
             contactFailureCount++;
             contactFailedLogs.push({ row: headers.map(header => row[header]), error: contactError.message });
             continue;
           }
           contactId = newContact.id;
           contactSuccessCount++;
           contact_ids_array.push(newContact.id);
         }
         console.log('contactId : ',contactId);
 
            // Function to find matching field IDs
 const findMatchingFieldIds = (headers, allCustomFields) => {
   return headers.reduce((acc, header) => {
     const match = allCustomFields.find(field => field.name.toLowerCase() === header.toLowerCase() && field.pipeline_id === pipeline_id);
     if (match) acc.push({ header, id: match.id, type: match.type, dropdown: match.dropdown });
     return acc;
   }, []);
 };
 
 const availableCustomFields = findMatchingFieldIds(headers, allCustomFields);
 
// console.log('availableCustomFields :', availableCustomFields);
 let customFieldsFlag = false;
 
 if (availableCustomFields.length > 0) {
   for (const field of availableCustomFields) {
     const fieldValue = row[field.header] || null;
 
     // Skip validation for blank values
     if (fieldValue === null || fieldValue === undefined || fieldValue === '') {
       continue;
     }
 
     // Validate value based on the type
     if (field.type === 'Date') {
       const dateRegex = /^\d{4}-\d{2}-\d{2}$/; // Regex for YYYY-MM-DD format
       if (!dateRegex.test(fieldValue) || isNaN(Date.parse(fieldValue))) {
         console.error(`Invalid date value for field: ${field.header}`);
         leadFailedLogs.push({ 
           row: headers.map(header => row[header]), 
           error: `Invalid date value for field: ${field.header}` 
         });
         leadFailureCount++;
         customFieldsFlag = true;
         continue;
       }
     } else if (field.type === 'Number') {
       if (isNaN(fieldValue)) {
         console.error(`Invalid number value for field: ${field.header}`);
         leadFailedLogs.push({ 
           row: headers.map(header => row[header]), 
           error: `Invalid number value for field: ${field.header}` 
         });
         leadFailureCount++;
         customFieldsFlag = true;
         continue;
       }
     } else if (field.type === 'Dropdown') {
       if (!field.dropdown.includes(fieldValue)) {
         console.error(`Invalid dropdown value for field: ${field.header}`);
         leadFailedLogs.push({ 
           row: headers.map(header => row[header]), 
           error: `Invalid dropdown value for field: ${field.header}` 
         });
         leadFailureCount++;
         customFieldsFlag = true;
         continue;
       }
     }
   }
 }
 
 if (customFieldsFlag) {
   continue;
 }
 
 // check if lead already exist for this contact id'

 // declare new lead variable outside if statement to avoid not defined issue of block scope
 let newLead;

 const { data: existingLead, error: fetchLeadError } = await supabase
           .from('crm_leads')
           .select('*')
           .eq('contact_id', contactId);

           console.log('existingLead : ',existingLead);
           
           if(existingLead.length>0){
            console.log('latest salesperson crm id : ',existingLead[existingLead.length-1].assigned_to);

              // For Duplicate leads, apply duplicate lead check.
           if(reassign=='skip'){
            lead_skip_count++;
            
            lead_skipped_Logs.push({ row: headers.map(header => row[header]), error: 'Lead Already Exist' });
            continue;
           }else
           if(reassign=='reassign'){
            // Assign Lead to latest salesperson
const leadData = {
  contact_id: contactId,
description: row.description,
requirement: row.requirement,
amount: row.amount ? parseFloat(row.amount) : null,
source: row.source,
title: row.title,
close_date: row.close_date || null,
created_by: imported_by,
assigned_by: imported_by,
stage: stageId,
workspace_id: workspaceId,
assigned_to: existingLead[existingLead.length-1].assigned_to,
last_update_at: new Date().toISOString(),
bulk_upload : 'Yes'

};

const { data, error: leadError } = await supabase
  .from('crm_leads')
  .insert(leadData)
  .select('id')
  .single();

if (leadError) {
  console.log('leadError : ',leadError);
  leadFailureCount++;
  leadFailedLogs.push({ row: headers.map(header => row[header]), error: leadError.message });
  continue;
} else {
  leadSuccessCount++;
  newLead = data;
  lead_ids_array.push(newLead.id);
}


           }
           

           }
            if(reassign=='duplicate' || existingLead.length==0){
// Insert lead data
const leadData = {
  contact_id: contactId,
description: row.description,
requirement: row.requirement,
amount: row.amount ? parseFloat(row.amount) : null,
source: row.source,
title: row.title,
close_date: row.close_date || null,
created_by: imported_by,
assigned_by: imported_by,
stage: stageId,
workspace_id: workspaceId,
assigned_to: assignedToId,
last_update_at: new Date().toISOString(),
bulk_upload : 'Yes'

};

const { data, error: leadError } = await supabase
  .from('crm_leads')
  .insert(leadData)
  .select('id')
  .single();

if (leadError) {
  console.log('leadError : ',leadError);
  leadFailureCount++;
  leadFailedLogs.push({ row: headers.map(header => row[header]), error: leadError.message });
  continue;
} else {
  newLead = data;
  lead_ids_array.push(newLead.id);
  leadSuccessCount++;
}
           }


         
 
         // Add data into lead custom fields table
 
         
 if (availableCustomFields.length > 0) {
   for (const field of availableCustomFields) {
     const fieldValue = row[field.header] || null;
     
     if(fieldValue !== null && fieldValue !== undefined && fieldValue !== ''){
       const { error: custom_field_values_error } = await supabase
       .from('crm_leads_custom_fields_values')
       .insert({
         workspace_id: workspaceId,
         lead_id: newLead.id,
         field_id: field.id,
         field_value: fieldValue
       });
       if (custom_field_values_error) {
         console.error('Failed to add values record', custom_field_values_error);
       }
     }
     }
     
     
 
    
 }
  
       } catch (error) {
                       console.error('Error processing row:', error);
                       leadFailedLogs.push({ row: headers.map(header => row[header]), error: error.message });
                     }
       }
 
       
     let formatted_contact_failed_logs,formatted_lead_failed_logs,formatted_company_failed_logs;
     let formatted_contact_skipped_Logs, formatted_lead_skipped_Logs, formatted_company_skipped_Logs;
 
     if(contactFailedLogs.length>0){
       formatted_contact_failed_logs = {
         head: headers,
         data: contactFailedLogs 
       };
     }else{
       formatted_contact_failed_logs = {};
     }
 
     if(leadFailedLogs.length>0){
       formatted_lead_failed_logs = {
         head: headers,
         data: leadFailedLogs 
       };
     }else{
       formatted_lead_failed_logs = {};
     }
 
     if(companyFailedLogs.length>0){
       formatted_company_failed_logs = {
         head: headers,
         data: companyFailedLogs 
       };
     }else{
       formatted_company_failed_logs = {};
     }
 
     if(contact_skipped_Logs.length>0){
       formatted_contact_skipped_Logs = {
         head: headers,
         data: contact_skipped_Logs 
       };
     }else{
       formatted_contact_skipped_Logs = {};
     }
 
     if(company_skipped_Logs.length>0){
       formatted_company_skipped_Logs = {
         head: headers,
         data: company_skipped_Logs 
       };
     }else{
       formatted_company_skipped_Logs = {};
     } 
 
     if(lead_skipped_Logs.length>0){
       formatted_lead_skipped_Logs = {
         head: headers,
         data: company_skipped_Logs 
       };
     }else{
       formatted_lead_skipped_Logs = {};
     } 
     
     
     const import_id = await logImportHistory({ source: fileName, module: 'Leads', added: leadSuccessCount, skipped: 0, imported_by:imported_by,workspace_id : workspaceId,failed_logs :formatted_lead_failed_logs,failed : leadFailureCount,skipped_logs : formatted_lead_skipped_Logs,successful_ids_array : lead_ids_array,parent_import_id : null});
     await logImportHistory({ source: fileName, module: 'Companies', added: companySuccessCount, skipped: companySkipCount, imported_by:imported_by,workspace_id : workspaceId,failed_logs :formatted_company_failed_logs,failed : companyFailureCount,skipped_logs : formatted_company_skipped_Logs,successful_ids_array : company_ids_array,parent_import_id :import_id});
     await logImportHistory({ source: fileName, module: 'Contacts', added: contactSuccessCount, skipped: contactSkipCount,imported_by:imported_by,workspace_id : workspaceId,failed_logs :formatted_contact_failed_logs,failed : contactFailureCount,skipped_logs : formatted_contact_skipped_Logs,successful_ids_array : contact_ids_array,parent_import_id :import_id});
     
      //  console.log('leadSuccessCount : ',leadSuccessCount)
      //  console.log('leadFailureCount : ',leadFailureCount)
      //  console.log('contactSuccessCount : ',contactSuccessCount)
      //  console.log('contactFailureCount : ',contactFailureCount)
      //  console.log('companySuccessCount : ',companySuccessCount)
      //  console.log('companyFailureCount : ',companyFailureCount)
       
       res.status(200).json({
         success: true,
         message: 'Import process completed',
         leadSuccessCount,
         lead_skip_count,
         leadFailureCount,
         contactSuccessCount,
         contactSkipCount,
         contactFailureCount,
         companySuccessCount,
         companySkipCount,
         companyFailureCount
       });
 
     });
   
    
 
 });

 // bulk upload lead using pipeline
 router.post("/upload-lead-with-pipeline/v1", uploadToMemory.single('csvFile'), async (req, res) => {
  // console.log(req);
   const workspaceId =  req.body.workspace_id;//Number(req.query.workspace_id);
   const imported_by = req.body.imported_by;//req.query.imported_by;
   const reassign = req.body.reassign;
   const pipeline_id = req.body.pipeline_id;
 
   console.log('workspaceId,imported_by,reassign : ',workspaceId,imported_by,reassign);
   if (!workspaceId || !imported_by || !reassign) {
     return res.status(400).json({ error: 'Missing workspace_id or imported_by or reassign settings' });
   }
 
   if (!req.file) {
     return res.status(400).json({ error: 'Missing CSV file' });
   }
 
   //const filePath = path.join(__dirname, req.file.path); // Path to the uploaded file
   const fileName = req.file.originalname; // Get the original file name
 
   console.log('fileName:', fileName); 
 
   const fileExtension = fileName.split('.').pop().toLowerCase();
   
   if (fileExtension !== 'csv') {
     return res.status(400).json({ error: 'Invalid file format. Only CSV files are allowed.' });
   }
   
   // Additionally, check MIME type if available
   if (req.file.mimetype && req.file.mimetype !== 'text/csv') {
     return res.status(400).json({ error: 'Invalid file type. Only CSV files are allowed.' });
   }
   // Parse CSV content
   const dataRows = [];
   const requiredColumns = ['first_name', 'email', 'phone', 'stage','assigned_to_email'];
   
   let headers = [];
     
 
   let contactFailedLogs = [], leadFailedLogs = [], companyFailedLogs = [];
   let contact_skipped_Logs = [], lead_skipped_Logs = [], company_skipped_Logs = [];
   
   let leadSuccessCount = 0,lead_skip_count = 0, leadFailureCount = 0;
   let contactSuccessCount = 0, contactFailureCount = 0, contactSkipCount = 0;
   let companySuccessCount = 0, companyFailureCount = 0, companySkipCount = 0;
   let formattedData = [];
   let lead_ids_array = [];
   let contact_ids_array = [];
   let company_ids_array = [];
         
  
 
   // Parse CSV content from in-memory buffer
 const bufferStream = require('stream').Readable.from(req.file.buffer); // Convert buffer to readable stream
 
 bufferStream
   .pipe(csv())
   .on('headers', (headerList) => {
     headers = headerList;
   })
   .on('data', (row) => {
     formattedData = headers.map(header => row[header]);
     dataRows.push(row);
   })
   
     .on('end', async () => {
        // console.log('Data Rows:', dataRows.length);
       const missingColumns = requiredColumns.filter(col => !headers.includes(col));
       if (missingColumns.length > 0) {

         /*
         leadFailedLogs.push({ row:formattedData, error: `Missing required columns: ${missingColumns.join(', ')}` });
     const  formatted_lead_failed_logs = {
         head: headers,
         data: leadFailedLogs 
       };
       await logImportHistory({ source: fileName, module: 'Leads', added: 0, skipped: 0, imported_by:imported_by,workspace_id : workspaceId,failed_logs :formatted_lead_failed_logs,failed : dataRows.length});
         */

         return res.status(400).json({ error: `Missing required columns: ${missingColumns.join(', ')}` });
       }
 
       
       // Fetch custom fields
       const { data: allCustomFields, error: fetchCustomFieldError } = await supabase
         .from('crm_leads_custom_fields_metadata')
         .select('*')
         .eq('workspace_id', workspaceId)
         .eq('pipeline_id', pipeline_id) ;

       if (fetchCustomFieldError) {
        // return res.status(500).json({ error: 'Error fetching custom fields' });
       }
       
           // Create column indices
           const columnIndices = {};
           headers.forEach((header) => {
             columnIndices[header] = header;
           });
 
       for (const row of dataRows) {
         try {
           // Initialize error collection for this row
           let rowErrors = [];
           let stageId = '', assignedToId = '', companyId = '', contactId = '';
           const stage_name = row.stage;

           // Fetch stage - but don't skip immediately on error
           const { data: existingStage, error: fetchStageError } = await supabase
             .from('crm_stages')
             .select('*')
             .eq('name', stage_name)
             .eq('pipeline', pipeline_id)
             .single();

           if (!existingStage || fetchStageError) {
             rowErrors.push(`Stage ${stage_name} not exists.`);
           } else {
             stageId = existingStage.id;
           }

           // Fetch assigned user profile - but don't skip immediately on error
           const { data: userProfile, error: userProfileError } = await supabase
             .from('user_profile')
             .select('id')
             .eq('email', row.assigned_to_email)
             .eq('workspace_id', workspaceId)
             .single();

           if (userProfileError || !userProfile) {
             rowErrors.push(`Salesperson ${row.assigned_to_email} not exists.`);
           } else {
             const user_id = userProfile.id;
             
             const { data: crmTeam, error: crmTeamError } = await supabase
               .from('crm_team')
               .select('id')
               .eq('user_id', user_id)
               .single();

             if (crmTeamError || !crmTeam) {
               rowErrors.push(`${row.assigned_to_email} could not be found in CRM Team.`);
             } else {
               assignedToId = crmTeam.id;
             }
           }

           // Check company validations
           const company_name = row.company_name;
           if (company_name) {
             const { data: existingCompany, error: fetchCompanyError } = await supabase
               .from('crm_companies')
               .select('*')
               .eq('name', company_name)
               .eq('workspace_id', workspaceId)
               .single();

             if (existingCompany) {
               companySkipCount++;
               companyId = existingCompany.id;
               company_skipped_Logs.push({ row: headers.map(header => row[header]), error: `Company Already Exist with this name.` });
             } else {
               // We'll actually insert the company later if there are no row errors
               companyId = 'pending'; // Mark as pending for now
             }
           } else {
             companyId = null;
           }

           // Sanitize and validate contact data
           const sanitizedPhone = row.phone ? row.phone.replace(/\D/g, '') : '';
           const sanitizedEmail = row.email ? String(row.email).trim() : '';

           if ((!sanitizedPhone && !sanitizedEmail) || !row.first_name || !row.title || !row.source) {
            rowErrors.push('Contact must have first name, phone, email, title and source.');
          }
          

           // Check if contact exists
           if (sanitizedPhone || sanitizedEmail) {
             const { data: existingContact, error: fetchContactError } = await supabase
               .from('crm_contacts')
               .select('*')
               .eq('phone', sanitizedPhone)
               .eq('email', sanitizedEmail)
               .eq('workspace_id', workspaceId)
               .single();

             if (existingContact) {
               contactSkipCount++;
               contactId = existingContact.id;
               contact_skipped_Logs.push({ row: headers.map(header => row[header]), error: 'Contact Already exist.' });
             } else {
               // We'll actually insert the contact later if there are no row errors
               contactId = 'pending'; // Mark as pending for now
             }
           }
 

           // Validate custom fields
           const findMatchingFieldIds = (headers, allCustomFields) => {
             return headers.reduce((acc, header) => {
               const match = allCustomFields.find(field => field.name.toLowerCase() == header.toLowerCase() && field.pipeline_id == pipeline_id);
               if (match) acc.push({ header, id: match.id, type: match.type, dropdown: match.dropdown });
               return acc;
             }, []);
           };
 
           const availableCustomFields = findMatchingFieldIds(headers, allCustomFields);

            

           if (availableCustomFields.length > 0) {
             for (const field of availableCustomFields) {
               const fieldValue = row[field.header] || null;

               // Skip validation for blank values
               if (fieldValue === null || fieldValue === undefined || fieldValue === '') {
                 continue;
               }

               // Validate value based on the type
               if (field.type === 'Date') {
                 const dateRegex = /^\d{4}-\d{2}-\d{2}$/; // Regex for YYYY-MM-DD format
                 if (!dateRegex.test(fieldValue) || isNaN(Date.parse(fieldValue))) {
                   rowErrors.push(`Invalid date value for field: ${field.header}`);
                 }
               } else if (field.type === 'Number') {
                 if (isNaN(fieldValue)) {
                   rowErrors.push(`Invalid number value for field: ${field.header}`);
                 }
               } else if (field.type === 'Dropdown') {
                 if (!field.dropdown.includes(fieldValue)) {
                   rowErrors.push(`Invalid dropdown value for field: ${field.header}`);
                 }
               }
             }
           }

           // If there are any errors in this row, log them all and skip to the next row
           if (rowErrors.length > 0) {
             leadFailureCount++;
             // Combine all errors into one message with line breaks or separators
             const combinedErrorMessage = rowErrors.join(' | ');
             leadFailedLogs.push({ row: headers.map(header => row[header]), error: combinedErrorMessage });
             continue; // Skip to next row
           }

           // If we reach here, there were no validation errors, so we can proceed with actual data insertion
           
           // Insert company if needed
           if (companyId === 'pending') {
             const companyData = {
               name: company_name,
               workspace_id: workspaceId,
               website: row.website,
               tax_no: row.tax_no,
               address: row[columnIndices?.company_billing_address],
               shipping_address: row[columnIndices?.company_shipping_address],
               city: row[columnIndices?.company_city],
               state: row[columnIndices?.company_state],
               pincode: row[columnIndices?.company_pincode] || null,
               country: row[columnIndices?.company_country],
             };
             
             const { data: newCompany, error: companyError } = await supabase
               .from('crm_companies')
               .insert(companyData)
               .select('id')
               .single();

             if (companyError) {
               companyFailureCount++;
               companyFailedLogs.push({ row: headers.map(header => row[header]), error: companyError.message });
               continue;
             }
             
             companyId = newCompany.id;
             companySuccessCount++;
             company_ids_array.push(newCompany.id);
           }

           // Insert contact if needed
           if (contactId === 'pending') {
             const contactData = {
               first_name: row.first_name,
               last_name: row.last_name,
               email: sanitizedEmail || null,
               phone: sanitizedPhone || null,
               address: row[columnIndices?.address] || null,
               country: row[columnIndices?.country] || null,
               country_code: row[columnIndices?.country_code] || '91',
               city: row[columnIndices?.city] || null,
               state: row[columnIndices?.state] || null,
               pincode: row[columnIndices?.pincode] || null,
               designation: row[columnIndices?.designation] || null,
               dob: row[columnIndices?.date_of_birth] || null,
               doa: row[columnIndices?.date_of_anniversary] || null,
               workspace_id: workspaceId,
               company_id: companyId,
               is_active: true,
               image_url: '',
               bulk_upload : 'Yes'
             };

             const { data: newContact, error: contactError } = await supabase
               .from('crm_contacts')
               .insert(contactData)
               .select('id')
               .single();

             if (contactError) {
               contactFailureCount++;
               contactFailedLogs.push({ row: headers.map(header => row[header]), error: contactError.message });
               continue;
             }
             
             contactId = newContact.id;
             contactSuccessCount++;
             contact_ids_array.push(newContact.id);
           }

           // Check if lead already exists
           let newLead;
           const { data: existingLead, error: fetchLeadError } = await supabase
             .from('crm_leads')
             .select('*')
             .eq('contact_id', contactId);

           if (existingLead && existingLead.length > 0) {
             // For Duplicate leads, apply duplicate lead check
             if (reassign == 'skip') {
               lead_skip_count++;
               lead_skipped_Logs.push({ row: headers.map(header => row[header]), error: 'Lead Already Exist' });
               continue;
             } else if (reassign == 'reassign') {
               // Assign Lead to latest salesperson
               const leadData = {
                 contact_id: contactId,
                 description: row.description,
                 requirement: row.requirement,
                 amount: row.amount ? parseFloat(row.amount) : null,
                 source: row.source,
                 title: row.title,
                 close_date: row.close_date || null,
                 created_by: imported_by,
                 assigned_by: imported_by,
                 stage: stageId,
                 workspace_id: workspaceId,
                 assigned_to: existingLead[existingLead.length-1].assigned_to,
                 last_update_at: new Date().toISOString(),
                 bulk_upload: 'Yes'
               };

               const { data, error: leadError } = await supabase
                 .from('crm_leads')
                 .insert(leadData)
                 .select('id')
                 .single();

               if (leadError) {
                 leadFailureCount++;
                 leadFailedLogs.push({ row: headers.map(header => row[header]), error: leadError.message });
                 continue;
               } else {
                 leadSuccessCount++;
                 newLead = data;
                 lead_ids_array.push(newLead.id);
               }
             }
           }
           
           if (reassign == 'duplicate' || !existingLead || existingLead.length == 0) {
             // Insert lead data
             const leadData = {
               contact_id: contactId,
               description: row.description,
               requirement: row.requirement,
               amount: row.amount ? parseFloat(row.amount) : null,
               source: row.source,
               title: row.title,
               close_date: row.close_date || null,
               created_by: imported_by,
               assigned_by: imported_by,
               stage: stageId,
               workspace_id: workspaceId,
               assigned_to: assignedToId,
               last_update_at: new Date().toISOString(),
               bulk_upload: 'Yes'
             };

             const { data, error: leadError } = await supabase
               .from('crm_leads')
               .insert(leadData)
               .select('id')
               .single();

             if (leadError) {
               leadFailureCount++;
               leadFailedLogs.push({ row: headers.map(header => row[header]), error: leadError.message });
               continue;
             } else {
               newLead = data;
               lead_ids_array.push(newLead.id);
               leadSuccessCount++;
             }
           }

           // Add data into lead custom fields table
           console.log(availableCustomFields.length,newLead)
           if (availableCustomFields.length > 0 && newLead) {
            console.log('inside custom fields',availableCustomFields.length);
             for (const field of availableCustomFields) {
               const fieldValue = row[field.header] || null;
               
               if (fieldValue !== null && fieldValue !== undefined && fieldValue !== '') {
                 const { error: custom_field_values_error } = await supabase
                   .from('crm_leads_custom_fields_values')
                   .insert({
                     workspace_id: workspaceId,
                     lead_id: newLead.id,
                     field_id: field.id,
                     field_value: fieldValue || null,
                   });
                   
                 if (custom_field_values_error) {
                   console.error('Failed to add values record', custom_field_values_error);
                 }
               }
             }
           }
           
         } catch (error) {
           console.error('Error processing row:', error);
           leadFailedLogs.push({ row: headers.map(header => row[header]), error: error.message });
           leadFailureCount++;
         }
       }

       
     let formatted_contact_failed_logs,formatted_lead_failed_logs,formatted_company_failed_logs;
     let formatted_contact_skipped_Logs, formatted_lead_skipped_Logs, formatted_company_skipped_Logs;
 
     if(contactFailedLogs.length>0){
       formatted_contact_failed_logs = {
         head: headers,
         data: contactFailedLogs 
       };
     }else{
       formatted_contact_failed_logs = {};
     }
 
     if(leadFailedLogs.length>0){
       formatted_lead_failed_logs = {
         head: headers,
         data: leadFailedLogs 
       };
     }else{
       formatted_lead_failed_logs = {};
     }
 
     if(companyFailedLogs.length>0){
       formatted_company_failed_logs = {
         head: headers,
         data: companyFailedLogs 
       };
     }else{
       formatted_company_failed_logs = {};
     }
 
     if(contact_skipped_Logs.length>0){
       formatted_contact_skipped_Logs = {
         head: headers,
         data: contact_skipped_Logs 
       };
     }else{
       formatted_contact_skipped_Logs = {};
     }
 
     if(company_skipped_Logs.length>0){
       formatted_company_skipped_Logs = {
         head: headers,
         data: company_skipped_Logs 
       };
     }else{
       formatted_company_skipped_Logs = {};
     } 
 
     if(lead_skipped_Logs.length>0){
       formatted_lead_skipped_Logs = {
         head: headers,
         data: lead_skipped_Logs 
       };
     }else{
       formatted_lead_skipped_Logs = {};
     } 
     
     
     const import_id = await logImportHistory({ source: fileName, module: 'Leads', added: leadSuccessCount, skipped: lead_skip_count, imported_by:imported_by,workspace_id : workspaceId,failed_logs :formatted_lead_failed_logs,failed : leadFailureCount,skipped_logs : formatted_lead_skipped_Logs,successful_ids_array : lead_ids_array,parent_import_id : null});
     await logImportHistory({ source: fileName, module: 'Companies', added: companySuccessCount, skipped: companySkipCount, imported_by:imported_by,workspace_id : workspaceId,failed_logs :formatted_company_failed_logs,failed : companyFailureCount,skipped_logs : formatted_company_skipped_Logs,successful_ids_array : company_ids_array,parent_import_id :import_id});
     await logImportHistory({ source: fileName, module: 'Contacts', added: contactSuccessCount, skipped: contactSkipCount,imported_by:imported_by,workspace_id : workspaceId,failed_logs :formatted_contact_failed_logs,failed : contactFailureCount,skipped_logs : formatted_contact_skipped_Logs,successful_ids_array : contact_ids_array,parent_import_id :import_id});
      
     // Fetch assigned user profile - but don't skip immediately on error
     const { data: import_by_details, error: import_by_details_error } = await supabase
     .from('user_profile')
     .select('*')
     .eq('id', imported_by)
     .single();

     if (!import_by_details_error) {
       console.log('import_by_details',import_by_details)
       await sendAdminEmail(
        import_by_details.email,
        'Lead Import Process Completed',
        '',
        `<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Import Summary</title>
  <style>
    body, .main {
      margin: 0;
      padding: 0;
      background-color: #f1f4f8;
      font-family: Arial, sans-serif;
      color: #333;
      padding-bottom: 20px;
    }

    .header {
      background-color: rgb(25, 219, 138);
      padding: 5px 10px;
      text-align: center;
    }

    .header > h1 {
      color: #fff;
      margin: 0;
    }

    .company-logo {
      max-width: 200px;
      height: auto;
      margin-top: 10px;
    }

    .email-container {
      max-width: 650px;
      margin: 0 auto;
      background-color: #fff;
      border-radius: 5px;
      border: #e0e3e7 1px solid;
      padding: 20px;
    }

    h1, p {
      margin: 0;
      color: #57636c;
    }

    .import-summary {
      margin-top: 20px;
      padding: 0 20px;
    }

    .import-summary p {
      margin-bottom: 10px;
    }

    .button {
      display: inline-block;
      padding: 10px 20px;
      text-decoration: none;
      background-color: #19db8a;
      color: #fff;
      border-radius: 5px;
      margin-top: 20px;
      text-align: center;
    }

    .footer {
      margin-top: 20px;
      color: #888;
      text-align: center;
    }

    .black {
      color: #14181b;
    }
  </style>
</head>

<body>
  <div class="main">
    <div class="company-logo-container" style="text-align: center; margin-bottom: 20px;">
      <img src="https://statics.myclickfunnels.com/image/1126739/file/5372994b496fdaf0460efe253b75a21d.png" alt="Company Logo" class="company-logo">
    </div>

    <div class="email-container">
      <div class="header">
        <h1>Import Summary</h1>
      </div>

      <div class="import-summary">
        <h4>Hi ${import_by_details.first_name},</h4>
        <p>The import process has been completed. Below is the summary:</p><br>

        <p><strong class="black">Leads</strong></p>
        <p>Successfully imported: ${leadSuccessCount}</p>
        <p>Skipped: ${lead_skip_count}</p>
        <p>Failed: ${leadFailureCount}</p><br>

        <p><strong class="black">Contacts</strong></p>
        <p>Successfully imported: ${contactSuccessCount}</p>
        <p>Skipped: ${contactSkipCount}</p>
        <p>Failed: ${contactFailureCount}</p><br>

        <p><strong class="black">Companies</strong></p>
        <p>Successfully imported: ${companySuccessCount}</p>
        <p>Skipped: ${companySkipCount}</p>
        <p>Failed: ${companyFailureCount}</p>
      </div>

      <a href="https://crm.automatebusiness.com/" class="button" target="_blank">Open CRM</a>

      <div class="footer">
        <p>This is an automated notification. Please do not reply.</p>
      </div>
    </div>
  </div>
</body>

</html>

`,
 '','',
 workspaceId,
'20ee8873-63c5-4f32-872d-f1b200bf4889'
       )
     }


       res.status(200).json({
         success: true,
         message: 'Import process completed',
         leadSuccessCount,
         lead_skip_count,
         leadFailureCount,
         contactSuccessCount,
         contactSkipCount,
         contactFailureCount,
         companySuccessCount,
         companySkipCount,
         companyFailureCount
       });
 
     });
});

//send-email-with-url-attachments


router.post(
  "/send-email-with-url-attachments",
  uploadToDisk.array("attachments"), // Handle file uploads
  async (req, res) => {
    try {
      console.log("Received request to /send-email-with-url-attachments");
      console.log("Request body:", req.body);
      console.log("Uploaded files:", req.files);

      const { user_id, to, subject, html, cc, bcc, leadId, attachmentUrls } =
        req.body;

      // Enhanced input validation
      const validateEmail = (email) => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
      };

      const parseEmails = (emailString) => {
        if (!emailString) return [];
        return emailString
          .split(",")
          .map((email) => email.trim())
          .filter((email) => validateEmail(email));
      };

      // Validate required fields
      if (!user_id || !to || !subject || !html || !leadId) {
        return res.status(400).json({
          error: "Missing required fields",
          details: "user_id, to, subject, leadId and html are mandatory",
        });
      }

      // Parse and validate email formats
      const toEmails = parseEmails(to);
      const ccEmails = parseEmails(cc);
      const bccEmails = parseEmails(bcc);

      if (toEmails.length === 0) {
        return res.status(400).json({ error: "Invalid recipient email format" });
      }

      if (cc && ccEmails.length === 0) {
        return res.status(400).json({ error: "Invalid CC email format" });
      }

      if (bcc && bccEmails.length === 0) {
        return res.status(400).json({ error: "Invalid BCC email format" });
      }

      // Validate attachmentUrls array
      let attachmentUrlsArray = [];
      if (attachmentUrls) {
        if (typeof attachmentUrls === "string") {
          attachmentUrlsArray = attachmentUrls.split(",").map((url) => url.trim());
        } else if (Array.isArray(attachmentUrls)) {
          attachmentUrlsArray = attachmentUrls;
        } else {
          return res
            .status(400)
            .json({ error: "attachmentUrls must be a string or an array" });
        }

        // Filter out empty or invalid URLs
        const validUrls = attachmentUrlsArray.filter(
          (url) => url && typeof url === "string"
        );

        // Add maximum attachment limit check
        const MAX_ATTACHMENTS = 10;
        const MAX_TOTAL_SIZE = 25 * 1024 * 1024; // 25MB total limit

        if (validUrls.length > MAX_ATTACHMENTS) {
          return res.status(400).json({
            error: `Maximum ${MAX_ATTACHMENTS} attachments allowed`,
          });
        }
      }

      // Fetch user credentials
      const { data: credentials, error: apiKeyError } = await supabase
        .from("workspace_email_credentials")
        .select("*")
        .eq("user_id", user_id)
        .single();

      if (apiKeyError || !credentials) {
        console.error("Error fetching credentials:", apiKeyError);
        return res.status(401).json({
          error: "Invalid or inactive API key",
          details: apiKeyError?.message,
        });
      }

      if (!credentials.refresh_token || !credentials.sender_mail_id) {
        return res.status(400).json({
          error: "Incomplete email credentials",
        });
      }

      // Fetch user profile
      const { data: userProfile, error: userProfileError } = await supabase
        .from("user_profile")
        .select("*")
        .eq("id", user_id)
        .single();

      if (userProfileError || !userProfile) {
        console.error("Error fetching user profile:", userProfileError);
        return res.status(400).json({
          error: "User not found or error in fetching user profile",
          details: userProfileError?.message,
        });
      }

      const workspaceId = userProfile.workspace_id;

      // Process tags and content
      let tags;
      try {
        tags = await fetchAndMergeData(leadId, workspaceId);
        if (!tags) {
          console.warn("No tags found for lead:", leadId);
        }
      } catch (error) {
        console.error("Error fetching tags:", error);
        return res.status(400).json({
          error: "Error processing tags",
          details: error.message,
        });
      }

      const processedHtml = replaceTags(html, tags);
      const processedSubject = replaceTags(subject, tags);

      // Process attachments
      const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB per file
      let attachments = [];

      // Process uploaded files
      if (req.files && req.files.length > 0) {
        console.log("Processing uploaded files:", req.files);

        req.files.forEach((file) => {
          if (file.size > MAX_FILE_SIZE) {
            console.warn(`File ${file.originalname} exceeds size limit`);
            return;
          }

          attachments.push({
            filename: file.originalname,
            content: require("fs").readFileSync(file.path),
            contentType: file.mimetype,
            contentDisposition: "attachment",
          });
        });
      }

      // Process attachment URLs
      if (attachmentUrlsArray.length > 0) {
        console.log("Processing attachment URLs:", attachmentUrlsArray);

        const processedAttachments = await Promise.all(
          attachmentUrlsArray.map(async (url) => {
            try {
              console.log(`Processing attachment URL:`, url);
              const attachment = await getAttachmentFromUrl(url);

              // Convert ArrayBuffer to Buffer
              const buffer = Buffer.from(attachment.content);

              // Check file size
              if (buffer.length > MAX_FILE_SIZE) {
                console.warn(`File ${attachment.filename} exceeds size limit`);
                return null;
              }

              return {
                filename: attachment.filename,
                content: buffer,
                contentType: attachment.contentType || "application/octet-stream",
                contentDisposition: "attachment",
              };
            } catch (error) {
              console.error(`Error processing attachment ${url}:`, error);
              return null;
            }
          })
        );

        // Filter out null values and add to attachments array
        attachments = attachments.concat(
          processedAttachments.filter((att) => att !== null)
        );
        console.log(`Successfully processed ${attachments.length} attachments`);
      }

      // Send email
      const REFRESH_TOKEN = credentials.refresh_token;
      const senderMail = credentials.sender_mail_id;
      const senderName = `${userProfile.first_name} ${userProfile.last_name}`;
      const mail_client = credentials.email_client;

      let emailInfo;
      try {
        if (mail_client === "gmail") {
          emailInfo = await sendEmail({
            REFRESH_TOKEN,
            senderMail,
            senderName,
            to: toEmails,
            processedSubject,
            processedHtml,
            cc: ccEmails,
            bcc: bccEmails,
            attachments,
          });
        } else if (mail_client === "outlook") {
          emailInfo = await sendOutlookEmail({
            REFRESH_TOKEN,
            to: toEmails,
            subject: processedSubject,
            htmlBody: processedHtml,
            cc: ccEmails,
            bcc: bccEmails,
            attachments,
          });
        }
      } catch (error) {
        console.error("Error sending email:", error);
        return res.status(500).json({
          error: "Failed to send email",
          details: error.message,
        });
      }

      // Log email
      try {
        await supabase.from("email_log").insert({
          event: "Test",
          sent_to: toEmails.join(", "),
          workspace_id: workspaceId,
          sending_email: senderMail,
          cc: ccEmails.join(", "),
          bcc: bccEmails.join(", "),
          subject: processedSubject,
          lead_id: leadId,
          user_id: user_id,
          attachments_count: attachments.length,
          status: "success",
        });
      } catch (error) {
        console.error("Error logging email:", error);
      }

      res.json({
        message: "Email sent successfully",
        info: emailInfo,
        attachments_processed: attachments.length,
      });
    } catch (error) {
      console.error("Error in /send-email-with-url-attachments route:", error);
      res.status(500).json({
        error: "Internal server error",
        message: error.message,
        stack: process.env.NODE_ENV === "development" ? error.stack : undefined,
      });
    }
  }
);

router.post("/send-email-to-contact", uploadToDisk.array("attachments"), async (req, res) => {
  try {
    const { user_id, contact_id, subject, html, cc, bcc } = req.body;
    const attachments = req.files;

    if (!user_id || !contact_id || !subject || !html) {
      return res.status(400).json({ error: "user_id, contact_id, subject, and html are mandatory." });
    }

    // Fetch contact details
    const { data: contact, error: contactError } = await supabase
      .from("crm_contacts")
      .select("*")
      .eq("id", contact_id)
      .single();

    if (contactError || !contact || !contact.email) {
      return res.status(404).json({ error: "Contact not found or missing email." });
    }

    // Fetch sender credentials
    const { data: credentials, error: apiKeyError } = await supabase
      .from("workspace_email_credentials")
      .select("*")
      .eq("user_id", user_id)
      .single();

    if (apiKeyError || !credentials) {
      return res.status(401).json({ error: "Invalid or inactive API key" });
    }

    // Fetch user profile
    const { data: userProfile, error: userProfileError } = await supabase
      .from("user_profile")
      .select("*")
      .eq("id", user_id)
      .single();

    if (userProfileError || !userProfile) {
      return res.status(400).json({ error: "User not found or error in fetching user profile" });
    }

    const workspaceId = userProfile.workspace_id;
    const REFRESH_TOKEN = credentials.refresh_token;
    const senderMail = credentials.sender_mail_id;
    const senderName = `${userProfile.first_name} ${userProfile.last_name}`;
    const mail_client = credentials.email_client;

    // Replace tags in subject/html using contact details
    const processedHtml = replaceTags(html, contact);
    const processedSubject = replaceTags(subject, contact);

    let emailInfo;
    if (mail_client === "gmail") {
      emailInfo = await sendEmail({
        REFRESH_TOKEN,
        senderMail,
        senderName,
        to: contact.email,
        processedSubject,
        processedHtml,
        cc,
        bcc,
        attachments,
      });
    } else if (mail_client === "outlook") {
      emailInfo = await sendOutlookEmail({
        REFRESH_TOKEN,
        to: contact.email,
        subject: processedSubject,
        htmlBody: processedHtml,
        cc,
        bcc,
        attachments,
      });
    }

    // Log the email
    await supabase.from("email_log").insert({
      event: "Send Email to Contact",
      sent_to: contact.email,
      workspace_id: workspaceId,
      sending_email: senderMail,
      cc,
      bcc,
      subject,
      contact_id,
      user_id,
    });

    res.json({ message: "Email sent successfully", info: emailInfo });
  } catch (error) {
    console.error("Error in /send-email-to-contact route:", error);
    res.status(500).json({ error: error.message, stack: error.stack });
  }
});

router.post("/send-bulk-email-to-contacts", uploadToDisk.array("attachments"), async (req, res) => {
  try {
    const { user_id, contact_ids, subject, html, cc, bcc } = req.body;
    const attachments = req.files;

    if (!user_id || !contact_ids || !subject || !html) {
      return res.status(400).json({ error: "user_id, contact_ids, subject, and html are mandatory." });
    }

    let contactIdArray = contact_ids;
    if (typeof contact_ids === "string") {
      // Support comma-separated string
      contactIdArray = contact_ids.split(",").map((id) => id.trim()).filter(Boolean);
    }
    if (!Array.isArray(contactIdArray) || contactIdArray.length === 0) {
      return res.status(400).json({ error: "contact_ids must be a non-empty array or comma-separated string." });
    }

    // Fetch sender credentials
    const { data: credentials, error: apiKeyError } = await supabase
      .from("workspace_email_credentials")
      .select("*")
      .eq("user_id", user_id)
      .single();

    if (apiKeyError || !credentials) {
      return res.status(401).json({ error: "Invalid or inactive API key" });
    }

    // Fetch user profile
    const { data: userProfile, error: userProfileError } = await supabase
      .from("user_profile")
      .select("*")
      .eq("id", user_id)
      .single();

    if (userProfileError || !userProfile) {
      return res.status(400).json({ error: "User not found or error in fetching user profile" });
    }

    const workspaceId = userProfile.workspace_id;
    const REFRESH_TOKEN = credentials.refresh_token;
    const senderMail = credentials.sender_mail_id;
    const senderName = `${userProfile.first_name} ${userProfile.last_name}`;
    const mail_client = credentials.email_client;

    let results = { success: [], failed: [] };

    for (const contact_id of contactIdArray) {
      try {
        // Fetch contact details
        const { data: contact, error: contactError } = await supabase
          .from("crm_contacts")
          .select("*")
          .eq("id", contact_id)
          .single();

        if (contactError || !contact || !contact.email) {
          results.failed.push({ contact_id, error: "Contact not found or missing email." });
          continue;
        }

        // Replace tags in subject/html using contact details
        const processedHtml = replaceTags(html, contact);
        const processedSubject = replaceTags(subject, contact);

        let emailInfo;
        if (mail_client === "gmail") {
          emailInfo = await sendEmail({
            REFRESH_TOKEN,
            senderMail,
            senderName,
            to: contact.email,
            processedSubject,
            processedHtml,
            cc,
            bcc,
            attachments,
          });
        } else if (mail_client === "outlook") {
          emailInfo = await sendOutlookEmail({
            REFRESH_TOKEN,
            to: contact.email,
            subject: processedSubject,
            htmlBody: processedHtml,
            cc,
            bcc,
            attachments,
          });
        }

        // Log the email
        await supabase.from("email_log").insert({
          event: "Send Bulk Email to Contact",
          sent_to: contact.email,
          workspace_id: workspaceId,
          sending_email: senderMail,
          cc,
          bcc,
          subject: processedSubject,
          contact_id,
          user_id,
        });

        results.success.push({ contact_id, email: contact.email });
      } catch (error) {
        results.failed.push({ contact_id, error: error.message });
      }
    }

    res.json({ message: "Bulk email process completed", results });
  } catch (error) {
    console.error("Error in /send-bulk-email-to-contacts route:", error);
    res.status(500).json({ error: error.message, stack: error.stack });
  }
});

module.exports = router;
