// mailer.js
const { google } = require("googleapis");
const nodemailer = require("nodemailer");

/*POPULATE BELOW FIELDS WITH YOUR CREDENTIALS*/

// const CLIENT_ID =
//   "***********-n9q8tocmppq3g6vrl3enfvqpdhepk6lm.apps.googleusercontent.com";
// const CLIENT_SECRET = "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";
const CLIENT_ID =
  "778802178451-04sd4rmj3ub0n779vo0m01qkfo9tmeum.apps.googleusercontent.com";
const CLIENT_SECRET = "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";
const REDIRECT_URI = "https://backend.automatebusiness.com/api/oauth2callback"; //DONT EDIT THIS

const { createClient } = require("@supabase/supabase-js");

// Create a single supabase client for interacting with your database
const supabase = createClient(
  "https://dudkeydyykleosvsoxgk.supabase.co",
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR1ZGtleWR5eWtsZW9zdnNveGdrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MDc4MjY0MTIsImV4cCI6MjAyMzQwMjQxMn0.DbngQhMRUmPXes80wAoG4ScYyO8L-FijfTIocvn72U0"
);

/*POPULATE ABOVE FIELDS WITH YOUR CREDENTIALS*/

const oAuth2Client = new google.auth.OAuth2(
  CLIENT_ID,
  CLIENT_SECRET,
  REDIRECT_URI
);

function getInlineImages(body) {
  const imgTags = body.match(/<img[^>]+>/g) || [];
  const inlineImages = {};

  imgTags.forEach((imgTag, i) => {
    const base64Match = imgTag.match(
      /src="data:image\/([a-zA-Z]*);base64,([^"]*)"/
    );

    if (base64Match) {
      const mimeType = base64Match[1];
      const base64Data = base64Match[2];
      const cid = `image${i}@example.com`;

      const imgBlob = Buffer.from(base64Data, "base64");

      const newImgTag = imgTag.replace(base64Match[0], `src="cid:${cid}"`);
      body = body.replace(imgTag, newImgTag);

      inlineImages[cid] = {
        filename: `image${i}.${mimeType}`,
        content: imgBlob,
        cid,
      };
    }
  });

  return {
    htmlBody: body,
    inlineImages: Object.values(inlineImages),
  };
}

// const sendEmail = async ({
//   REFRESH_TOKEN,
//   senderMail,
//   to,
//   subject,
//   html,
//   cc,
//   bcc,
//   attachments,
// }) => {
//   console.log(
//     "logs in send email function.",
//     REFRESH_TOKEN,
//     senderMail,
//     to,
//     subject,
//     html,
//     cc,
//     bcc
//   );

//   oAuth2Client.setCredentials({ refresh_token: REFRESH_TOKEN });

//   const ACCESS_TOKEN = await oAuth2Client.getAccessToken();
//   console.log(ACCESS_TOKEN);
//   const transport = nodemailer.createTransport({
//     service: "gmail",
//     auth: {
//       type: "OAuth2",
//       user: senderMail,
//       clientId: CLIENT_ID,
//       clientSecret: CLIENT_SECRET,
//       refreshToken: REFRESH_TOKEN,
//       accessToken: ACCESS_TOKEN,
//     },
//     tls: {
//       rejectUnauthorized: true,
//     },
//   });

//   // EMAIL OPTIONS
//   const mailOptions = {
//     from: senderMail,
//     to,
//     subject,
//     html,
//     cc,
//     bcc,
//     attachments: [],
//     // attachments: attachments.map((file) => ({
//     //   filename: file.originalname,
//     //   content: file.buffer,
//     // })),
//   };

//   // if (attachment) {
//   //   mailOptions.attachments.push({
//   //     filename: attachment.originalname,
//   //     content: attachment.buffer,
//   //   });
//   // }

//   console.log("Sending email to:", to);

//   return new Promise((resolve, reject) => {
//     transport.sendMail(mailOptions, (err, info) => {
//       if (err) {
//         console.error("Error sending email:", err);
//         reject(err);
//       } else {
//         console.log("Email sent:", info);
//         resolve(info);
//       }
//     });
//   });
// };
// const sendEmail = async ({
//   REFRESH_TOKEN,
//   senderMail,
//   to,
//   processedSubject,
//   processedHtml,
//   cc,
//   bcc,
//   attachments,
// }) => {
//   console.log(
//     "logs in send email function.",
//     REFRESH_TOKEN,
//     senderMail,
//     to,
//     processedSubject,
//     processedHtml,
//     cc,
//     bcc
//   );

//   oAuth2Client.setCredentials({ refresh_token: REFRESH_TOKEN });

//   const ACCESS_TOKEN = await oAuth2Client.getAccessToken();
//   const { htmlBody, inlineImages } = getInlineImages(processedHtml);

//   console.log(ACCESS_TOKEN.token);
//   const transport = nodemailer.createTransport({
//     service: "gmail",
//     auth: {
//       type: "OAuth2",
//       user: senderMail,
//       clientId: CLIENT_ID,
//       clientSecret: CLIENT_SECRET,
//       refreshToken: REFRESH_TOKEN,
//       accessToken: ACCESS_TOKEN.token,
//     },
//     tls: {
//       rejectUnauthorized: false,
//     },
//   });

//   const mailOptions = {
//     from: senderMail,
//     to,
//     subject: processedSubject,
//     html: htmlBody,
//     cc,
//     bcc,
//     attachments: [
//       ...(attachments|| []).map((file) => ({
//         filename: file.originalname,
//         content: file.buffer,
//       })),
//       ...inlineImages,
//     ],
//   };

//   console.log("Sending email to:", to);

//   return new Promise((resolve, reject) => {
//     transport.sendMail(mailOptions, (err, info) => {
//       if (err) {
//         console.error("Error sending email:", err);
//         reject(err);
//       } else {
//         console.log("Email sent:", info);
//         resolve(info);
//       }
//     });
//   });
// };

/*
const sendEmail = async ({
  REFRESH_TOKEN,
  senderMail,
  senderName,
  to,
  processedSubject,
  processedHtml,
  cc,
  bcc,
  attachments,
}) => {

  // console.log(
  //   "logs in send email function.",
  //   REFRESH_TOKEN,
  //   senderMail,
  //   to,
  //   processedSubject,
  //   processedHtml,
  //   cc,
  //   bcc
  // );

  

  oAuth2Client.setCredentials({ refresh_token: REFRESH_TOKEN });

  try {
    const { htmlBody, inlineImages } = getInlineImages(processedHtml);

    const gmail = google.gmail({ version: "v1", auth: oAuth2Client });

    // Function to encode the email to base64 (required by Gmail API)
    const encodeEmail = (mailOptions, senderName) => {
      const boundary = "===============Boundary-XYZ1234567890ABCDEFG==";
      let message = "";
    
      message += `Content-Type: multipart/mixed; boundary="${boundary}"\r\n`;
      message += "MIME-Version: 1.0\r\n";
      message += `From: ${senderName} <${mailOptions.from}>\r\n`; // Include sender name
      message += `To: ${mailOptions.to}\r\n`;
      if (mailOptions.cc) message += `Cc: ${mailOptions.cc}\r\n`;
      if (mailOptions.bcc) message += `Bcc: ${mailOptions.bcc}\r\n`;
      message += `Subject: ${mailOptions.subject}\r\n\r\n`;
    
      message += `--${boundary}\r\n`;
      message += "Content-Type: text/html; charset=UTF-8\r\n";
      message += "Content-Transfer-Encoding: 7bit\r\n\r\n";
      message += `${mailOptions.html}\r\n\r\n`;
    
      // Add inline images
      if (mailOptions.inlineImages && Array.isArray(mailOptions.inlineImages)) { // Check if it exists and is an array
        for (const image of mailOptions.inlineImages) {
          message += `--${boundary}\r\n`;
          message += `Content-Type: ${image.contentType}\r\n`;
          message += "Content-Transfer-Encoding: base64\r\n";
          message += `Content-ID: ${image.cid}\r\n`; // Use image.cid here
          message += `Content-Disposition: inline; filename="${image.filename}"\r\n\r\n`;
          message += `${image.content}\r\n\r\n`;
        }
      }
    
      // Add attachments
      if (mailOptions.attachments) {
        for (const file of mailOptions.attachments) {
          message += `--${boundary}\r\n`;
          message += `Content-Type: application/octet-stream\r\n`; // Adjust content type if needed
          message += "Content-Transfer-Encoding: base64\r\n";
          message += `Content-Disposition: attachment; filename="${file.filename}"\r\n\r\n`;
          message += `${file.content.toString("base64")}\r\n\r\n`;
        }
      }
    
      message += `--${boundary}--\r\n`;
    
      return Buffer.from(message).toString("base64").replace(/\+/g, "-").replace(/\//g, "_").replace(/=+$/, "");
    };    
    

    const mailOptions = {
      to,
      subject: processedSubject,
      html: htmlBody,
      cc,
      bcc,
      attachments: [
        ...(attachments || []).map((file) => ({
          filename: file.originalname,
          content: file.buffer,
        })),
        ...inlineImages,
      ],
    };

    const rawMessage = encodeEmail(mailOptions, senderName);

    console.log("Sending email to:", to);

    const res = await gmail.users.messages.send({
      userId: "me",
      requestBody: {
        raw: rawMessage,
      },
    });

    console.log("Email sent:", res.data);
    return res.data;
  } catch (err) {
    console.error("Error sending email:", err);
    throw err; // Re-throw the error to be handled upstream
  }
}; */

const sendEmail = async ({
  REFRESH_TOKEN,
  senderMail,
  senderName,
  to,
  processedSubject,
  processedHtml,
  cc,
  bcc,
  attachments,
}) => {
  try {
    oAuth2Client.setCredentials({ refresh_token: REFRESH_TOKEN });

    const gmail = google.gmail({ version: "v1", auth: oAuth2Client });

    // Function to encode the email
    const encodeEmail = (mailOptions) => {
      const boundary = "foo_bar_baz";
      let message = [
        `From: ${senderName} <${mailOptions.from}>`,
        `To: ${mailOptions.to}`,
        cc ? `Cc: ${mailOptions.cc}` : "", // Add CC header if provided
        bcc ? `Bcc: ${mailOptions.bcc}` : "", // Add BCC header if provided
        `Subject: ${mailOptions.subject}`,
        'MIME-Version: 1.0',
        `Content-Type: multipart/mixed; boundary="${boundary}"`,
        '',
        `--${boundary}`,
        'Content-Type: text/html; charset=utf-8',
        '',
        mailOptions.html,
        ''
      ];

      // Add attachments
      if (mailOptions.attachments && mailOptions.attachments.length > 0) {
        mailOptions.attachments.forEach(attachment => {
          message = message.concat([
            `--${boundary}`,
            `Content-Type: ${attachment.contentType}`,
            'Content-Transfer-Encoding: base64',
            `Content-Disposition: attachment; filename="${attachment.filename}"`,
            '',
            attachment.content.toString('base64'),
            ''
          ]);
        });
      }

      message.push(`--${boundary}--`);

      return Buffer.from(message.join('\r\n'))
        .toString('base64')
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=+$/, '');
    };

    const mailOptions = {
      from: senderMail,
      to,
      cc,
      bcc,
      subject: processedSubject,
      html: processedHtml,
      attachments,
    };

    const raw = encodeEmail(mailOptions);

    const result = await gmail.users.messages.send({
      userId: 'me',
      requestBody: {
        raw
      }
    });

    return result.data;
  } catch (error) {
    console.error('Error in sendEmail:', error);
    throw error;
  }
};



const smtpTransportOptions = {
  host: "smtp.zeptomail.in",
  auth: {
    user: "emailapikey",
    pass: "PHtE6r0JRODt2m4t9xEC7f7uEZSsNNkm/ullKFRFtd0TCKJSGE0HqNwvwWOwqEp/VqZFEqadnIo9sr6Yu+qHLWfvNTlJWWqyqK3sx/VYSPOZsbq6x00ZsF4cdkfZXIPocdBp0yXUvt3eNA==",
  },
  tls: {
    ciphers: "SSLv3",
  },
  // If you're using a service like Gmail or another known service, use this field, otherwise remove it.
  // service: process.env.SMTP_SERVICE,  // Only if you need it, otherwise, remove
};

const transporter = nodemailer.createTransport(smtpTransportOptions);
async function sendZeptomail(
  to,
  subject,
  text,
  html,
  cc // Optional cc parameter
) {
  const mailOptions = {
    from: "Business Workspace <<EMAIL>>",
    to,
    cc, // CC recipients (can be a single address or comma-separated multiple addresses)
    subject,
    text,
    html,
  };

  try {
    const info = await transporter.sendMail(mailOptions);
    console.log("Email sent: ", info.response);
  } catch (error) {
    console.error("Error sending email:", error);
    // Handle errors appropriately, e.g., log to a database or send retry notifications
  }
}


// function to send email from admin with new nameInMail option
async function sendAdminEmail(to, subject, text, html, cc, attachments = [],workspace_id,user_id) {
  const mailOptions = {
    from: "Automate Business <<EMAIL>>",
    to,
    cc,
    subject,
    text,
    html,
    attachments,
  };

  try {
    const info = await transporter.sendMail(mailOptions);
    console.log("Email sent: ", info.response);
        // Insert task record
        const email_log = {
          workspace_id: workspace_id,
          sent_by : user_id ,
          status: info.response,
          cc: cc,
         
          attachments: attachments,
          subject : subject,
          body :  text + '\n' + html
          
        };
    
        const { data : newMailRecord,error: mailLogError } = await supabase
          .from('admin_emails_log')
          .insert(email_log);
    
        
  } catch (error) {
    console.error("Error sending email:", error);
    throw error;
  }
}

module.exports = { sendEmail, oAuth2Client, sendZeptomail,sendAdminEmail };
