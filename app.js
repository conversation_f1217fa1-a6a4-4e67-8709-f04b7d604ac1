const express = require("express");
const faceapi = require("@vladmandic/face-api/dist/face-api.node.js");
const { Canvas, Image } = require("canvas");
const canvas = require("canvas");
const morgan = require("morgan");
const fileUpload = require("express-fileupload");
const bodyParser = require("body-parser")
const routes = require("./routes");
faceapi.env.monkeyPatch({ Canvas, Image });


const { createClient } = require("@supabase/supabase-js");

// Create a single supabase client for interacting with your database
const supabase = createClient(
  "https://dudkeydyykleosvsoxgk.supabase.co",
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR1ZGtleWR5eWtsZW9zdnNveGdrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MDc4MjY0MTIsImV4cCI6MjAyMzQwMjQxMn0.DbngQhMRUmPXes80wAoG4ScYyO8L-FijfTIocvn72U0"
);

const app = express();
// app.use(
//   fileUpload({
//     useTempFiles: true,
//   })
// );

app.use(bodyParser.urlencoded({extended:false}));
app.use(morgan("dev"));
app.use(express.json());
app.use(function (req, res, next) {
  res.header("Access-Control-Allow-Origin", "*");
  res.header("Access-Control-Allow-Methods", "GET,HEAD,OPTIONS,POST,PUT");
  res.header(
    "Access-Control-Allow-Headers",
    "Origin, X-Requested-With, Content-Type, Accept, Authorization"
  );
  next();
});
app.use("/api", routes);

async function LoadModels() {
  // Load the models
  // __dirname gives the root directory of the server
  await faceapi.nets.faceRecognitionNet.loadFromDisk(__dirname + "/models");
  await faceapi.nets.faceLandmark68Net.loadFromDisk(__dirname + "/models");
  await faceapi.nets.ssdMobilenetv1.loadFromDisk(__dirname + "/models");
}
LoadModels();

app.listen(process.env.PORT || 3030);
console.log("DB connected and server us running.");
