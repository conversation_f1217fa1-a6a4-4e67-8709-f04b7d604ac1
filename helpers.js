const axios = require("axios");
const sharp = require("sharp");
const faceapi = require("@vladmandic/face-api/dist/face-api.node.js");
const { Canvas, Image, loadImage, createCanvas } = require("canvas");
const canvas = require("canvas");
const { createClient } = require("@supabase/supabase-js");
const multer  = require('multer')

faceapi.env.monkeyPatch({ Canvas, Image });

const supabase = createClient(
  "https://dudkeydyykleosvsoxgk.supabase.co",
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR1ZGtleWR5eWtsZW9zdnNveGdrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MDc4MjY0MTIsImV4cCI6MjAyMzQwMjQxMn0.DbngQhMRUmPXes80wAoG4ScYyO8L-FijfTIocvn72U0"
);

async function uploadLabeledImages(data) {
  const images = data.faces;
  try {
    let counter = 0;
    const descriptions = [];
    // Loop through the images
    for (let i = 0; i < images.length; i++) {
      const img = await canvas.loadImage(images[i]);
      // let img = await loadImage(images[i]);

      // // Rotate the image if its height is greater than its width
      // if (img.height > img.width) {
      //   const canvas = createCanvas(img.height, img.width);
      //   const ctx = canvas.getContext("2d");
      //   ctx.translate(canvas.width / 2, canvas.height / 2);
      //   ctx.rotate((90 * Math.PI) / 180);
      //   ctx.drawImage(img, -img.width / 2, -img.height / 2);
      //   img = canvas;
      // }
      counter = (i / images.length) * 100;
      console.log(`Progress = ${counter}%`);
      // Read each face and save the face descriptions in the descriptions array
      const detections = await faceapi
        .detectSingleFace(img)
        .withFaceLandmarks()
        .withFaceDescriptor();
      descriptions.push(detections.descriptor);
    }

    // Create a new face document with the given label and save it in DB
    const { data: updatedRecord, error } = await supabase
      .from("faces_db")
      .update({
        descriptions: descriptions,
      })
      .eq("id", data.id);
    if (error) console.log(error);
    return true;
  } catch (error) {
    console.log(error);
    return error;
  }
}

async function getDescriptorsFromDB(image, user_id) {
  // Get all the face data from mongodb and loop through each of them to read the data
  const { data, error } = await supabase
    .from("faces_db")
    .select()
    .eq("user_id", user_id);
  if (error) console.log(error);
  let faces = data;
    // console.log(faces);
  for (i = 0; i < faces.length; i++) {
    // Change the face data descriptors from Objects to Float32Array type
    for (j = 0; j < faces[i].descriptions.length; j++) {
      faces[i].descriptions[j] = new Float32Array(
        Object.values(faces[i].descriptions[j])
      );
    }
    // Turn the DB face docs to
    faces[i] = new faceapi.LabeledFaceDescriptors(
      faces[i].user_id,
      faces[i].descriptions
    );
  }

  // Load face matcher to find the matching face
  const faceMatcher = new faceapi.FaceMatcher(faces, 0.6);

  // Read the image using canvas or other method
  // const img = await canvas.loadImage(image);

  // Read the image using canvas or other method
  let img = await canvas.loadImage(image);

  // Rotate the image if its height is greater than its width
  // if (img.height > img.width) {
  //   const canvas = createCanvas(img.height, img.width);
  //   const ctx = canvas.getContext("2d");
  //   ctx.translate(canvas.width / 2, canvas.height / 2);
  //   ctx.rotate((90 * Math.PI) / 180);
  //   ctx.drawImage(img, -img.width / 2, -img.height / 2);
  //   img = canvas;
  // }

  let temp = faceapi.createCanvasFromMedia(img);
  // Process the image for the model
  const displaySize = { width: img.width, height: img.height };
  faceapi.matchDimensions(temp, displaySize);

  // Find matching faces
  const detections = await faceapi
    .detectAllFaces(img)
    .withFaceLandmarks()
    .withFaceDescriptors();
  const resizedDetections = faceapi.resizeResults(detections, displaySize);
  const results = resizedDetections.map((d) =>
    faceMatcher.findBestMatch(d.descriptor)
  );
  console.log(results);
  const found = results.filter((r) => r._label === user_id);
  console.log(found);
  return found.length == 1 ? true : false;
}
// Function to replace tags in HTML with leadData values
function replaceTags(html, leadData) {
  return html.replace(/{{(\w+)}}/g, (match, tagName) => {
    return leadData[tagName] || match;
  });
}

async function loadImageAsBuffer(imageUrl) {
  try {
    const response = await axios.get(imageUrl, {
      responseType: "arraybuffer",
      timeout: 30000,
    });
    return Buffer.from(response.data, "binary");
  } catch (error) {
    console.error("Error downloading image:", error.message);
    throw error;
  }
}

async function preprocessImage(imageBuffer) {
  try {
    const image = sharp(imageBuffer);
    const metadata = await image.metadata();

    // Normalize orientation
    let normalizedImage = image.rotate();

    // Resize if the image is too large
    if (metadata.width > 1024 || metadata.height > 1024) {
      normalizedImage = normalizedImage.resize(1024, 1024, { fit: "inside" });
    }

    // Convert to JPEG format and get as buffer
    const processedBuffer = await normalizedImage.toFormat("jpeg").toBuffer();

    // Create a canvas from the processed image
    const img = await canvas.loadImage(processedBuffer);
    const cvs = canvas.createCanvas(img.width, img.height);
    const ctx = cvs.getContext("2d");
    ctx.drawImage(img, 0, 0);

    return cvs;
  } catch (error) {
    console.error("Error preprocessing image:", error.message);
    throw error;
  }
}

async function detectFace(imageCanvas) {
  try {
    const detections = await faceapi
      .detectAllFaces(imageCanvas)
      .withFaceLandmarks()
      .withFaceDescriptors();
    return detections;
  } catch (error) {
    console.error("Error detecting faces:", error.message);
    throw error;
  }
}

async function compareImageWithAllOrientations(imageUrl, userId) {
  console.log(`Starting face comparison for user_id: ${userId}`);
  console.log("Image source:", imageUrl);

  try {
    const imageBuffer = await loadImageAsBuffer(imageUrl);
    const processedCanvas = await preprocessImage(imageBuffer);

    // Detect faces in the processed image
    const detections = await detectFace(processedCanvas);

    if (detections.length === 0) {
      console.log("No faces detected in the image.");
      return false;
    }

    // Get user's face descriptors from the database
    const userDescriptors = await getUserFaceDescriptors(userId);

    if (!userDescriptors || userDescriptors.length === 0) {
      console.log("No face descriptors found for the user.");
      return false;
    }

    // Compare detected faces with user's face descriptors
    const faceMatcher = new faceapi.FaceMatcher(userDescriptors, 0.6);

    for (const detection of detections) {
      const match = faceMatcher.findBestMatch(detection.descriptor);
      if (match._label === userId) {
        console.log("Match found for the user.");
        return true;
      }
    }

    console.log("No match found for the user.");
    return false;
  } catch (error) {
    console.error("Error in compareImageWithAllOrientations:", error.message);
    return false;
  }
}

async function getUserFaceDescriptors(userId) {
  // Fetch user's face descriptors from the database
  const { data, error } = await supabase
    .from("faces_db")
    .select("descriptions")
    .eq("user_id", userId);

  if (error) {
    console.error("Error fetching user face descriptors:", error);
    return null;
  }

  if (!data || data.length === 0) {
    console.log("No face data found for the user.");
    return null;
  }

  // Convert the descriptions to FaceDescriptor objects
  return data[0].descriptions.map(
    (desc) =>
      new faceapi.LabeledFaceDescriptors(userId, [
        new Float32Array(Object.values(desc)),
      ])
  );
}

async function compareImageWithWorkspace(imageUrl, workspaceId) {
  console.log(`Starting face comparison for workspace_id: ${workspaceId}`);
  console.log("Image source:", imageUrl);

  try {
    // Load and preprocess the input image
    const imageBuffer = await loadImageAsBuffer(imageUrl);
    const processedCanvas = await preprocessImage(imageBuffer);

    // Detect faces in the processed image
    const detections = await detectFace(processedCanvas);

    if (detections.length === 0) {
      console.log("No faces detected in the image.");
      return null;
    }

    // Get all face descriptors from the workspace
    const { data: workspaceUsers, error } = await supabase
      .from("faces_db")
      .select("user_id, descriptions")
      .eq("workspace_id", workspaceId);

    if (error) {
      console.error("Error fetching workspace face descriptors:", error);
      return null;
    }

    if (!workspaceUsers || workspaceUsers.length === 0) {
      console.log("No face data found for the workspace.");
      return null;
    }

    // Convert all workspace users' descriptions to LabeledFaceDescriptors
    const workspaceDescriptors = workspaceUsers.map(user => {
      // Convert the descriptions to Float32Array
      const descriptors = user.descriptions.map(desc => 
        new Float32Array(Object.values(desc))
      );
      return new faceapi.LabeledFaceDescriptors(user.user_id, descriptors);
    });

    // Create face matcher with all workspace users
    const faceMatcher = new faceapi.FaceMatcher(workspaceDescriptors, 0.6);

    // Check each detected face against the workspace users
    for (const detection of detections) {
      const match = faceMatcher.findBestMatch(detection.descriptor);
      // If we find a match (not 'unknown'), return the user_id
      if (match._label !== 'unknown') {
        console.log(`Match found for user: ${match._label}`);
        return match._label;
      }
    }

    console.log("No matching users found in the workspace.");
    return null;

  } catch (error) {
    console.error("Error in compareImageWithWorkspace:", error.message);
    return null;
  }
}

async function fetchAndMergeData(leadId, workspaceId) {
  console.log('inside fetchAndMergeData');
  try {
    // Fetch fields from the stored procedure
    const { data: fields, error: fieldsError } = await supabase
      .rpc('get_crm_merge_tags', { p_lead_id: leadId, p_workspace_id: workspaceId });



    if (fieldsError) {
      throw fieldsError;
    }

    // Initialize the result object
    let result = {};

    // Fetch Lead Standard Fields
    const leadStandardFields = fields
      .filter(field => field.source === 'Lead Standard Field')
      .map(field => field.merge_tag);
    
      

    const { data: leadStandardData, error: leadStandardError } = await supabase
      .from('crm_leads')
      .select(leadStandardFields.join(', '))
      .eq('id', leadId)
      .eq('workspace_id', workspaceId);

    if (leadStandardError) {
      throw leadStandardError;
    }

    // Merge lead standard data into result object
    if (leadStandardData.length > 0) {
      Object.assign(result, leadStandardData[0]);
    }

    // Fetch Lead Custom Fields
    const { data: leadCustomFields, error: leadCustomFieldsError } = await supabase
      .from('crm_leads_custom_fields_metadata')
      .select('*')
      .eq('workspace_id', workspaceId);

    if (leadCustomFieldsError) {
      throw leadCustomFieldsError;
    }
   //console.log('leadCustomFields',leadCustomFields);

    const leadCustomFieldIds = leadCustomFields.map(field => field.id);
    
    const { data: leadCustomValues, error: leadCustomValuesError } = await supabase
      .from('crm_leads_custom_fields_values')
      .select('*')
      .eq('lead_id', leadId)
      .in('field_id', leadCustomFieldIds);

    if (leadCustomValuesError) {
      throw leadCustomValuesError;
    }

    // Merge lead custom data into result object
    leadCustomValues.forEach(customValue => {
      const fieldName = leadCustomFields.find(field => field.id === customValue.field_id).name;
      result[`lead_custom_${fieldName}`] = customValue.field_value;
    });

    const { data: leadData, error: leadError } = await supabase
    .from('crm_leads_view')
    .select('*')
    .eq('id', leadId)
    .single();
   
   if (leadError || !leadData) {
    console.log('Error in Fetching lead data');
    console.log(leadError);
    return res.status(401).json({ error: 'Error in fetching lead data, please check leadId properly.' });
   }
   console.log('lead data is : ',leadData);

    // Fetch Contact Standard Fields
    const contactStandardFields = fields
      .filter(field => field.source === 'Contact Standard Field')
      .map(field => field.merge_tag);

    const { data: contactStandardData, error: contactStandardError } = await supabase
      .from('crm_contacts')
      .select(contactStandardFields.join(', '))
      .eq('id', leadData.contact_id)
      .eq('workspace_id', workspaceId);

    if (contactStandardError) {
      throw contactStandardError;
    }

    // Merge contact standard data into result object
    if (contactStandardData.length > 0) {
      Object.assign(result, contactStandardData[0]);
    }

    
   // Fetch Contact Custom Fields
   const { data: contactCustomFields, error: contactCustomFieldsError } = await supabase
   .from('crm_contacts_custom_fields_metadata')
   .select('*')
   .eq('workspace_id', workspaceId);

 if (contactCustomFieldsError) {
   throw contactCustomFieldsError;
 }
   
 


 const contactCustomFieldIds = contactCustomFields.map(field => field.id);
 const { data: contactCustomValues, error: contactCustomValuesError } = await supabase
   .from('crm_contacts_custom_fields_values')
   .select('*')
   .eq('contact_id', leadData.contact_id)
   .in('field_id', contactCustomFieldIds);

 if (contactCustomValuesError) {
   throw contactCustomValuesError;
 }

 // Merge contact custom data into result object
 contactCustomValues.forEach(customValue => {
   const fieldName = contactCustomFields.find(field => field.id === customValue.field_id).name;
   result[`contact_custom_${fieldName}`] = customValue.field_value;
 });

 // Get company_id from the contact data
 let companyId = null;
 if (contactStandardData.length > 0 && contactStandardData[0].company_id) {
   companyId = contactStandardData[0].company_id;
 }
 
 // If we have a company_id, fetch Company Standard Fields
 if (companyId) {
   const companyStandardFields = fields
     .filter(field => field.source === 'Company Standard Field')
     .map(field => field.merge_tag);

   if (companyStandardFields.length > 0) {
     const { data: companyStandardData, error: companyStandardError } = await supabase
       .from('crm_companies')
       .select(companyStandardFields.join(', '))
       .eq('id', companyId)
       .eq('workspace_id', workspaceId);

     if (companyStandardError) {
       throw companyStandardError;
     }

     // Merge company standard data into result object
     if (companyStandardData.length > 0) {
       Object.assign(result, companyStandardData[0]);
     }
   }

   // Fetch Company Custom Fields
   const { data: companyCustomFields, error: companyCustomFieldsError } = await supabase
     .from('crm_company_custom_fields_metadata')
     .select('*')
     .eq('workspace_id', workspaceId);

   if (companyCustomFieldsError) {
     throw companyCustomFieldsError;
   }

   const companyCustomFieldIds = companyCustomFields.map(field => field.id);
   
   if (companyCustomFieldIds.length > 0) {
     const { data: companyCustomValues, error: companyCustomValuesError } = await supabase
       .from('crm_company_custom_fields_values')
       .select('*')
       .eq('company_id', companyId)
       .in('field_id', companyCustomFieldIds);

     if (companyCustomValuesError) {
       throw companyCustomValuesError;
     }

     // Merge company custom data into result object
     companyCustomValues.forEach(customValue => {
       const fieldName = companyCustomFields.find(field => field.id === customValue.field_id).name;
       result[`company_custom_${fieldName}`] = customValue.field_value;
     });
   }
 }


result.stage_name = leadData.stage_name;
   console.log('variables values is : ',result);
    return result;
  } catch (error) {
    console.error('Error fetching data:', error);
    return null;
  }
}

async function getValuefromdatabase(tableName, matchColumn, matchValue) {
  const { data: tableRecord, error: fetchError } = await supabase
    .from(tableName)
    .select("*")
    .eq(matchColumn, matchValue)
    .limit(1)
    .single();
  //console.log('tableRecord',tableRecord)
  if (fetchError) {
    return fetchError;
  } else {
    return tableRecord;
  }
}

const getEmailRecord = async (template_name) => {
  const { data: emailRecord, error: emailRecordError } = await supabase
    .from("email_templates")
    .select("subject, html_content")
    .eq("template_name", template_name)
    .single();
  if (emailRecordError) {
    console.error("Error fetching email record:", emailRecordError);
    return null; // Or handle the error as needed
  }
  console.log("emailRecord:", emailRecord);
  return emailRecord; // Optionally return the record if needed
};

const replaceTemplateVariables = (template, variables) => {
  return template.replace(/\{(.*?)\}/g, (match, key) => {
    return variables[key.trim()] || "";
  });
};



/*
async function logImportHistory({ source, module, added, skipped, imported_by,workspace_id,failed_logs,failed,skipped_logs,successful_ids_array }) {
  console.log('testxsh',source, module, added, skipped, imported_by,workspace_id,failed_logs,failed,skipped_logs,successful_ids_array)
  const { error: historyError } = await supabase.from('crm_import_history').insert({ source, module, added, skipped, imported_by,workspace_id,failed_logs,failed,skipped_logs,successful_ids_array });
  if (historyError) {
    console.error('Failed to add import history record', historyError);
  } else {
    console.log('Import history record added successfully');
  }
}*/


async function logImportHistory({ 
  source, 
  module, 
  added, 
  skipped, 
  imported_by,
  workspace_id,
  failed_logs,
  failed,
  skipped_logs,
  successful_ids_array,
  parent_import_id
}) {
  //console.log('testxsh', source, module, added, skipped, imported_by, workspace_id, failed_logs, failed, skipped_logs, successful_ids_array);

  const { data, error: historyError } = await supabase
    .from('crm_import_history')
    .insert({
      source,
      module,
      added,
      skipped,
      imported_by,
      workspace_id,
      failed_logs,
      failed,
      skipped_logs,
      successful_ids_array,
      parent_import_id
    })
    .select('id') // 👈 this tells Supabase to return the inserted row's `id`
    .single();    // 👈 this tells it to return just one object instead of an array

  if (historyError) {
    console.error('Failed to add import history record', historyError);
    return null;
  } else {
    console.log('Import history record added successfully, ID:', data.id);
    return data.id; // 👈 return the new record's ID
  }
}

 // Multer configuration for file uploads
 const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, "uploads/"); // Upload directory
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + "-" + Math.round(Math.random() * 1e9);
    cb(null, uniqueSuffix + "-" + file.originalname); // Unique file name
  },
});

const upload = multer({
  storage,
  limits: { fileSize: 10 * 1024 * 1024 }, // Limit file size to 10MB
});

// Function to fetch attachment data from a URL, including Google Drive support
async function getAttachmentFromUrl(url) {
  if (!url || typeof url !== "string") {
    throw new Error("Invalid or missing URL for attachment.");
  }
  
  const defaultFilename = "attachment"; // Fallback filename
  let fileBuffer, filename;
  
  // Extract file ID from Google Drive URL or handle other URLs
  const match = url.match(/\/d\/(.*?)\/|id=(.*?)(?:&|$)/);
  const fileId = match?.[1] || match?.[2];
  
  if (fileId) {
    // Handle Google Drive URLs
    const driveDownloadUrl = `https://drive.google.com/uc?export=download&id=${fileId}`;
    const response = await fetch(driveDownloadUrl);
    
    if (!response.ok) {
      throw new Error(`Failed to fetch Google Drive file: ${response.statusText}`);
    }
    
    // Use arrayBuffer() instead of buffer()
    fileBuffer = await response.arrayBuffer();
    
    // Try to get the filename from the response headers
    const contentDisposition = response.headers.get("content-disposition");
    filename = contentDisposition?.match(/filename="(.*?)"/)?.[1] || defaultFilename;
  } else {
    // Handle other direct file URLs
    const response = await fetch(url);
    
    if (!response.ok) {
      throw new Error(`Failed to fetch file from URL: ${response.statusText}`);
    }
    
    // Use arrayBuffer() instead of buffer()
    fileBuffer = await response.arrayBuffer();
    
    // Extract filename from URL if available
    const urlFilename = url.split("/").pop()?.split("?")[0];
    filename = urlFilename || defaultFilename;
  }
  
  return {
    filename,
    content: fileBuffer,
  };
}


// Function to fetch and process attachments
async function getAttachments(attachmentUrls = [], uploadedFiles = []) {
  const urlAttachments = await Promise.all(
    attachmentUrls.map(async (url) => {
      try {
        const { filename, content } = await getAttachmentFromUrl(url);
        return { filename, content };
      } catch (error) {
        console.error("Error fetching attachment from URL:", url, error.message);
        return null; // Skip invalid attachments
      }
    })
  );

  const fileAttachments = uploadedFiles.map((file) => ({
    filename: file.originalname,
    path: file.path,
  }));

  return [...urlAttachments.filter(Boolean), ...fileAttachments];
}

const domain = "https://crmapi.automatebusiness.com/api/meta";


const sendDynamicMessage = async function (mobileNo, templateName, token, phoneNoId, wabaId, header = {}, body = [], wid, wa_channel_id, schedule) {
  const endPoint = `${domain}/v19.0/${phoneNoId}/messages`;
  let template = await getTemplateByName(token, templateName, wabaId);
  
  if (Object.keys(template).length !== 0) {
    let data = {
      to: mobileNo,
      recipient_type: "individual",
      type: "template",
      template: {
        language: {
          policy: "deterministic",
          code: template['language']
        },
        name: template['name'],
        components: [
          {
            type: "header",
            parameters: []
          },
          {
            type: "body",
            parameters: []
          }
        ]
      }
    };

    if (schedule) {
      data.schedule = new Date(schedule).toISOString();
    }

    if (header.TEXT) {
      data.template.components[0].parameters.push({ type: "text", text: header.TEXT });
    }
    if (header.IMAGE) {
      data.template.components[0].parameters.push({ type: "image", image: { link: getLink(header.IMAGE) } });
    }
    if (header.VIDEO) {
      data.template.components[0].parameters.push({ type: "video", video: { link: getLink(header.VIDEO) } });
    }
    if (header.DOCUMENT) {
      data.template.components[0].parameters.push({ type: "document", document: { link: getLink(header.DOCUMENT) } });
    }

    if (body.length > 0) {
      for (let i = 0; i < body.length; i++) {
        data.template.components[1].parameters.push({ type: "text", text: body[i] });
      }
    }

    const options = {
      method: "POST",
      headers: {
        "Authorization": "Bearer " + token,
        "Content-Type": "application/json"
      },
      body: JSON.stringify(data)
    };

    try {
      const response = await fetch(endPoint, options);
      if (response.ok) {
        console.log(await response.text());
        await supabase.from("wa_log").insert({
          event: templateName,
          sent_to: data.to,
          worksapce_id: wid,
          sending_number: wa_channel_id,
        });
        console.log("Message sent successfully!");
        return true;
      } else {
        console.log("Error in sending message", response.status, response.statusText);
        return false;
      }
    } catch (error) {
      console.error("Fetch error:", error);
      return false;
    }
  } else {
    console.log("Error in getting template details!");
    return false;
  }
};

/**
 * Retrieves Approved templates details by name.
 *
 * @param {string} token - The API token for authentication.
 * @param {string} templateName - The name of the template to retrieve.
 * @param {string} wabaId - The WABA ID to fetch template details.
 * @returns {Object} - Returns the template details object.
 */
async function getTemplateByName(token, templateName, wabaId) {
  const endPoint = `${domain}/v19.0/${wabaId}/message_templates?name=${templateName}`;

  const options = {
    headers: {
      "Authorization": "Bearer " + token
    }
  };

  try {
    const response = await fetch(endPoint, options);
    if (response.ok) {
      const responseData = await response.json();
      if (responseData && responseData.data && responseData.data.length > 0) {
        for (let i = 0; i < responseData.data.length; i++) {
          if (responseData.data[i]['status'] == "APPROVED" && responseData.data[i]['name'] == templateName) {
            return responseData.data[i];
          }
        }
      }
      return {};
    }
  } catch (e) {
    console.log(e.stack);
    return {};
  }
}

/**
 * Extracts the file ID from the Google Drive URL.
 *
 * @param {string} url - The Google Drive URL.
 * @returns {string|null} - Returns the file ID or null if not found.
 */
function getDriveFileIdFromUrl(url) {
  const match = url.match(/[-\w]{25,}/);
  return match ? match[0] : null;
}

/**
 * Modifies Google Drive URLs to direct download links.
 *
 * @param {string} link - The original Google Drive URL.
 * @returns {string} - Returns the direct download link.
 */
function getLink(link) {
  if (link.includes('docs.google.com') || link.includes('drive.google.com')) {
    return `https://drive.google.com/uc?export=download&id=${getDriveFileIdFromUrl(link)}`
  } else {
    return link;
  }
}


// Function to log product import history
async function log_product_import({ 
  source, 
  module, 
  added, 
  skipped, 
  imported_by, 
  workspace_id, 
  failed_logs, 
  failed, 
  skipped_logs,
  successful_ids_array 
}) {
  const { error: historyError } = await supabase
    .from('crm_import_history')
    .insert({ 
      source, 
      module, 
      added, 
      skipped, 
      imported_by, 
      workspace_id, 
      failed_logs, 
      failed, 
      skipped_logs,
      successful_ids_array
    });
    
  if (historyError) {
    console.error('Failed to add import history record', historyError);
  } else {
    console.log('Import history record added successfully');
  }
}



module.exports = {

  uploadLabeledImages,
  getDescriptorsFromDB,
  supabase,
  replaceTags,
  fetchAndMergeData,
  compareImageWithAllOrientations,
  compareImageWithWorkspace,
  getValuefromdatabase,
  getEmailRecord,
  replaceTemplateVariables,
  logImportHistory,
  getAttachments,
  sendDynamicMessage,
  log_product_import,
  getAttachmentFromUrl
  
};



